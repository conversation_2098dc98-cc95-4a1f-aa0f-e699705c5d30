import { getSupabaseAdmin } from "@/lib/supabase"

/**
 * Optimized database queries to reduce N+1 query patterns
 * and improve performance across the application
 */

export interface DashboardStats {
  totalEvents: number
  totalRegistrations: number
  totalRevenue: number
  totalWithdrawals: number
  attendanceRate: number
}

export interface EventWithDetails {
  id: string
  title: string
  slug: string
  description: string
  location: string
  start_date: string
  end_date: string
  image_url: string | null
  price: number
  is_published: boolean
  registrations_count: number
  organizer: {
    id: string
    name: string
  } | null
  category: {
    id: string
    name: string
    color: string
    icon: string | null
  } | null
}

/**
 * Fetch dashboard statistics with optimized single query
 */
export async function fetchDashboardStats(userId: string): Promise<DashboardStats> {
  const supabaseAdmin = getSupabaseAdmin()

  try {
    // Single optimized query to get all stats at once
    const { data: statsData, error } = await supabaseAdmin
      .rpc('get_dashboard_stats', { user_id: userId })

    if (error) {
      console.error("Error fetching dashboard stats:", error)
      // Return default stats if RPC doesn't exist
      return await fetchDashboardStatsLegacy(userId)
    }

    return statsData || {
      totalEvents: 0,
      totalRegistrations: 0,
      totalRevenue: 0,
      totalWithdrawals: 0,
      attendanceRate: 0
    }
  } catch (error) {
    console.error("Error in fetchDashboardStats:", error)
    return await fetchDashboardStatsLegacy(userId)
  }
}

/**
 * Legacy method for dashboard stats (fallback)
 */
async function fetchDashboardStatsLegacy(userId: string): Promise<DashboardStats> {
  const supabaseAdmin = getSupabaseAdmin()

  // Get total events created by user
  const { count: eventsCount } = await supabaseAdmin
    .from("events")
    .select("*", { count: "exact", head: true })
    .eq("event_manager_id", userId)

  // Get registrations for user's events with payment info
  const { data: registrationsData } = await supabaseAdmin
    .from("registrations")
    .select(`
      id,
      checked_in,
      payment_amount,
      payment_status,
      events!inner(event_manager_id)
    `)
    .eq("events.event_manager_id", userId)

  const totalRegistrations = registrationsData?.length || 0
  const checkedInRegistrations = registrationsData?.filter(reg => reg.checked_in).length || 0
  const attendanceRate = totalRegistrations > 0 ? (checkedInRegistrations / totalRegistrations) * 100 : 0

  // Calculate total revenue
  const totalRevenue = registrationsData
    ?.filter(reg => reg.payment_status === "completed")
    .reduce((sum, reg) => sum + (reg.payment_amount || 0), 0) || 0

  // Get withdrawals
  const { data: withdrawalData } = await supabaseAdmin
    .from("transactions")
    .select("amount")
    .eq("transaction_type", "withdrawal")
    .eq("status", "paid")
    .eq("user_id", userId)

  const totalWithdrawals = withdrawalData?.reduce((sum, item) => sum + (item.amount || 0), 0) || 0

  return {
    totalEvents: eventsCount || 0,
    totalRegistrations,
    totalRevenue,
    totalWithdrawals,
    attendanceRate
  }
}

/**
 * Fetch events with all related data in a single optimized query
 */
export async function fetchEventsWithDetails(
  filters: {
    userId?: string
    isPublished?: boolean
    limit?: number
    offset?: number
  } = {}
): Promise<EventWithDetails[]> {
  const supabaseAdmin = getSupabaseAdmin()

  let query = supabaseAdmin
    .from("events")
    .select(`
      id,
      title,
      slug,
      description,
      location,
      start_date,
      end_date,
      image_url,
      price,
      is_published,
      registrations(id),
      organizations:organization_id(id, name),
      category:category_id(id, name, color, icon)
    `)

  // Apply filters
  if (filters.userId) {
    query = query.eq("event_manager_id", filters.userId)
  }

  if (filters.isPublished !== undefined) {
    query = query.eq("is_published", filters.isPublished)
  }

  // Apply pagination
  if (filters.limit) {
    query = query.limit(filters.limit)
  }

  if (filters.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
  }

  const { data, error } = await query

  if (error) {
    console.error("Error fetching events with details:", error)
    return []
  }

  // Transform the data to include registration count
  const transformedData = (data || []).map(event => ({
    ...event,
    registrations_count: event.registrations?.length || 0,
    registrations: undefined // Remove the registrations array to clean up response
  }))

  // Sort events: available events first (by start_date), then ended events last
  return transformedData.sort((a, b) => {
    const now = new Date();
    const aEnded = new Date(a.end_date) < now;
    const bEnded = new Date(b.end_date) < now;

    // If one is ended and the other isn't, prioritize the non-ended one
    if (aEnded && !bEnded) return 1;
    if (!aEnded && bEnded) return -1;

    // If both have the same status (both ended or both available), sort by start_date
    return new Date(a.start_date).getTime() - new Date(b.start_date).getTime();
  })
}

/**
 * Fetch user tickets with event details in optimized query
 */
export async function fetchUserTickets(userId: string) {
  const supabaseAdmin = getSupabaseAdmin()

  const { data: tickets, error } = await supabaseAdmin
    .from("registrations")
    .select(`
      *,
      event:event_id (
        id,
        title,
        slug,
        description,
        location,
        start_date,
        end_date,
        image_url,
        price,
        is_published,
        organizer:organization_id(id, name),
        category:category_id(id, name, color, icon)
      )
    `)
    .or(`user_id.eq.${userId},created_by.eq.${userId}`)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching user tickets:", error)
    return []
  }

  return tickets || []
}

/**
 * Batch update multiple records efficiently
 */
export async function batchUpdateRegistrations(
  updates: Array<{ id: string; updates: Record<string, any> }>
) {
  const supabaseAdmin = getSupabaseAdmin()

  // Use Promise.all for concurrent updates (be careful with rate limits)
  const results = await Promise.allSettled(
    updates.map(({ id, updates: updateData }) =>
      supabaseAdmin
        .from("registrations")
        .update(updateData)
        .eq("id", id)
    )
  )

  const successful = results.filter(result => result.status === "fulfilled").length
  const failed = results.filter(result => result.status === "rejected").length

  return { successful, failed, total: updates.length }
}
