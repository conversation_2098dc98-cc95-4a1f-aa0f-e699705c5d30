import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { createPayment, getActivePaymentGateways } from "@/lib/payment-gateway";
import { logActivity } from "@/lib/activity-logger";

/**
 * POST /api/registrations/payment
 * Initiates payment for a pending registration
 * Requires authentication
 */
export async function POST(request: Request) {
  try {
    console.log("Registration Payment API: Starting request");

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("Registration Payment API: Token present:", !!token);

    if (!token) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      );
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token);
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    const userId = authResult.user.id;
    console.log("Registration Payment API: User ID:", userId);

    // Parse request body
    const body = await request.json();
    const { registration_id, amount, currency = 'MYR', description, gateway_id } = body;

    // Validate required fields
    if (!registration_id || !amount) {
      return NextResponse.json(
        { error: "Registration ID and amount are required" },
        { status: 400 }
      );
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();

    // Verify the registration exists and belongs to the user
    const { data: registration, error: regError } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        event:event_id (
          id,
          title,
          price,
          organization_id
        )
      `)
      .eq("id", registration_id)
      .or(`user_id.eq.${userId},created_by.eq.${userId}`)
      .single();

    if (regError || !registration) {
      console.error("Registration not found:", regError);
      return NextResponse.json(
        { error: "Registration not found or access denied" },
        { status: 404 }
      );
    }

    // Check if registration is already paid
    if (registration.payment_status === 'paid') {
      return NextResponse.json(
        { error: "Registration is already paid" },
        { status: 400 }
      );
    }

    // Get active payment gateways
    const paymentGateways = await getActivePaymentGateways();
    if (!paymentGateways || paymentGateways.length === 0) {
      return NextResponse.json(
        { error: "No payment gateways available" },
        { status: 500 }
      );
    }

    // Use the specified gateway or the first available one
    let selectedGateway = paymentGateways[0];
    if (gateway_id) {
      const requestedGateway = paymentGateways.find(gw => gw.id === gateway_id);
      if (requestedGateway) {
        selectedGateway = requestedGateway;
      } else {
        return NextResponse.json(
          { error: "Requested payment gateway not available" },
          { status: 400 }
        );
      }
    }

    // Create payment request
    const paymentRequest = {
      amount: parseFloat(amount.toString()),
      currency: currency,
      description: description || `Payment for ${registration.event?.title || 'Event Registration'}`,
      customer_email: authResult.user.email,
      customer_name: authResult.user.name || authResult.user.email,
      reference_id: `reg_${registration_id}_${Date.now()}`,
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/return`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/return`,
    };

    console.log("Registration Payment API: Creating payment with gateway:", selectedGateway.id);

    // Create payment with selected gateway
    const paymentResponse = await createPayment(selectedGateway.id, paymentRequest);

    if (!paymentResponse.success) {
      console.error("Payment creation failed:", paymentResponse.error);
      return NextResponse.json(
        {
          success: false,
          error: paymentResponse.error || "Failed to create payment",
        },
        { status: 500 }
      );
    }

    // Create transaction record
    const transactionData = {
      id: crypto.randomUUID(),
      user_id: authResult.user.id,
      registration_id: registration_id,
      gateway_id: selectedGateway.id,
      transaction_type: 'registration_payment',
      amount: parseFloat(amount.toString()),
      currency: currency,
      status: 'processing',
      gateway_transaction_id: paymentResponse.transaction_id,
      gateway_response: paymentResponse,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: transactionRecord, error: transactionError } = await supabaseAdmin
      .from("transactions")
      .insert([transactionData])
      .select()
      .single();

    if (transactionError) {
      console.error("Failed to create transaction record:", transactionError);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to create transaction record",
        },
        { status: 500 }
      );
    }

    // Update registration with payment and transaction details
    const { error: updateError } = await supabaseAdmin
      .from("registrations")
      .update({
        payment_status: 'processing',
        transaction_id: transactionRecord.id,
        payment_amount: parseFloat(amount.toString()),
        updated_at: new Date().toISOString()
      })
      .eq("id", registration_id);

    if (updateError) {
      console.error("Error updating registration:", updateError);
      return NextResponse.json(
        { error: "Failed to update registration" },
        { status: 500 }
      );
    }

    // Log activity
    await logActivity({
      action: "payment_initiated",
      user_id: userId,
      details: {
        registration_id: registration_id,
        amount: amount,
        currency: currency,
        payment_gateway: selectedGateway.id,
        transaction_id: paymentResponse.transaction_id,
      },
    });

    console.log("Registration Payment API: Payment created successfully");

    return NextResponse.json({
      success: true,
      payment_url: paymentResponse.payment_url,
      transaction_id: paymentResponse.transaction_id,
      gateway: selectedGateway.name,
      message: "Payment initiated successfully"
    });

  } catch (error) {
    console.error("Registration Payment API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
