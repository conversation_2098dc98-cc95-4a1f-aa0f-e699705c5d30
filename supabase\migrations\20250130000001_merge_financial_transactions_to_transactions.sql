-- Merge financial_transactions table into transactions table
-- This migration consolidates all transaction data into a single unified table

-- First, let's add any missing columns to transactions table that exist in financial_transactions
DO $$
BEGIN
  -- Add fee_amount column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'fee_amount') THEN
    ALTER TABLE transactions ADD COLUMN fee_amount DECIMAL(10,2) DEFAULT 0;
  END IF;
  
  -- Add payment_method column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'payment_method') THEN
    ALTER TABLE transactions ADD COLUMN payment_method VARCHAR(50);
  END IF;
  
  -- Add event_id column if it doesn't exist (for direct event association)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'event_id') THEN
    ALTER TABLE transactions ADD COLUMN event_id UUID REFERENCES events(id) ON DELETE SET NULL;
  END IF;
  
  -- Add net_amount column for calculated net amount after fees
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'net_amount') THEN
    ALTER TABLE transactions ADD COLUMN net_amount DECIMAL(10,2);
  END IF;
END $$;

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_transactions_event_id ON transactions(event_id);
CREATE INDEX IF NOT EXISTS idx_transactions_fee_amount ON transactions(fee_amount);
CREATE INDEX IF NOT EXISTS idx_transactions_payment_method ON transactions(payment_method);

-- Migrate data from financial_transactions to transactions
INSERT INTO transactions (
  id,
  user_id,
  registration_id,
  event_id,
  transaction_type,
  amount,
  fee_amount,
  currency,
  status,
  payment_method,
  gateway_transaction_id,
  net_amount,
  created_at,
  updated_at,
  metadata
)
SELECT 
  ft.id,
  ft.user_id,
  ft.registration_id,
  ft.event_id,
  CASE 
    WHEN ft.transaction_type = 'payment' THEN 'registration_payment'
    WHEN ft.transaction_type = 'withdrawal' THEN 'withdrawal'
    WHEN ft.transaction_type = 'refund' THEN 'refund'
    WHEN ft.transaction_type = 'system_fee' THEN 'system_fee'
    ELSE ft.transaction_type
  END as transaction_type,
  ft.amount,
  COALESCE(ft.fee_amount, 0) as fee_amount,
  'MYR' as currency,
  CASE 
    WHEN ft.status = 'completed' THEN 'paid'
    WHEN ft.status = 'pending' THEN 'pending'
    WHEN ft.status = 'failed' THEN 'failed'
    WHEN ft.status = 'cancelled' THEN 'cancelled'
    ELSE ft.status
  END as status,
  ft.payment_method,
  ft.payment_id as gateway_transaction_id,
  (ft.amount - COALESCE(ft.fee_amount, 0)) as net_amount,
  ft.created_at,
  ft.updated_at,
  jsonb_build_object(
    'migrated_from', 'financial_transactions',
    'original_payment_id', ft.payment_id,
    'payment_gateway_id', ft.payment_gateway_id
  ) as metadata
FROM financial_transactions ft
WHERE NOT EXISTS (
  SELECT 1 FROM transactions t WHERE t.id = ft.id
);

-- Update any registrations that reference financial_transactions to use the migrated transactions
UPDATE registrations 
SET transaction_id = ft.id
FROM financial_transactions ft
WHERE registrations.payment_id = ft.payment_id 
  AND registrations.transaction_id IS NULL
  AND ft.registration_id = registrations.id;

-- Generate invoice and receipt numbers for migrated transactions that don't have them
WITH numbered_transactions AS (
  SELECT
    id,
    'INV' || (1000 + ROW_NUMBER() OVER (ORDER BY created_at)) as new_invoice_number,
    CASE
      WHEN status = 'paid' THEN 'RCP' || (1000 + ROW_NUMBER() OVER (ORDER BY created_at))
      ELSE NULL
    END as new_receipt_number,
    CASE
      WHEN status = 'paid' THEN updated_at
      ELSE NULL
    END as new_processed_at
  FROM transactions
  WHERE invoice_number IS NULL
    AND metadata->>'migrated_from' = 'financial_transactions'
)
UPDATE transactions
SET
  invoice_number = nt.new_invoice_number,
  receipt_number = nt.new_receipt_number,
  processed_at = nt.new_processed_at
FROM numbered_transactions nt
WHERE transactions.id = nt.id;

-- Ensure all free event registrations have proper transaction records
INSERT INTO transactions (
  id,
  user_id,
  registration_id,
  event_id,
  transaction_type,
  amount,
  fee_amount,
  currency,
  status,
  gateway_transaction_id,
  net_amount,
  processed_at,
  created_at,
  updated_at,
  metadata
)
SELECT 
  gen_random_uuid() as id,
  r.user_id,
  r.id as registration_id,
  r.event_id,
  'registration_payment' as transaction_type,
  0 as amount,
  0 as fee_amount,
  'MYR' as currency,
  'paid' as status,
  'FREE-' || UPPER(SUBSTRING(r.id::text, 1, 8)) as gateway_transaction_id,
  0 as net_amount,
  r.payment_date as processed_at,
  r.created_at,
  r.updated_at,
  jsonb_build_object(
    'free_event', true,
    'auto_generated', true,
    'registration_id', r.id
  ) as metadata
FROM registrations r
LEFT JOIN transactions t ON t.registration_id = r.id
WHERE r.payment_amount = 0 
  AND r.payment_status = 'paid'
  AND t.id IS NULL;

-- Update registrations to link to the newly created free event transactions
UPDATE registrations 
SET transaction_id = t.id
FROM transactions t
WHERE registrations.id = t.registration_id
  AND registrations.transaction_id IS NULL
  AND t.metadata->>'free_event' = 'true';

-- Add comments for documentation
COMMENT ON COLUMN transactions.fee_amount IS 'Transaction fee amount charged by payment gateway or system';
COMMENT ON COLUMN transactions.payment_method IS 'Payment method used (online_banking, credit_card, etc.)';
COMMENT ON COLUMN transactions.event_id IS 'Direct reference to event for easier querying';
COMMENT ON COLUMN transactions.net_amount IS 'Net amount after deducting fees (amount - fee_amount)';

-- Create a view for backward compatibility (temporary)
CREATE OR REPLACE VIEW financial_transactions_view AS
SELECT
  id,
  user_id,
  event_id,
  registration_id,
  CASE
    WHEN transaction_type = 'registration_payment' THEN 'payment'
    ELSE transaction_type
  END as transaction_type,
  amount,
  fee_amount,
  CASE
    WHEN status = 'paid' THEN 'completed'
    ELSE status
  END as status,
  payment_method,
  gateway_transaction_id as payment_id,
  created_at,
  updated_at,
  metadata->>'payment_gateway_id' as payment_gateway_id
FROM transactions
WHERE metadata->>'migrated_from' = 'financial_transactions'
   OR transaction_type IN ('withdrawal', 'refund', 'system_fee');

-- Log the migration
INSERT INTO activity_logs (
  user_id,
  action,
  entity_type,
  entity_id,
  details,
  category
) VALUES (
  NULL,
  'migrate_financial_transactions',
  'system',
  gen_random_uuid(),
  jsonb_build_object(
    'migration', 'merge_financial_transactions_to_transactions',
    'timestamp', NOW(),
    'description', 'Merged financial_transactions table into transactions table for unified transaction management'
  ),
  'system_maintenance'
);
