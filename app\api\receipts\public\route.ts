import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import crypto from "crypto";

/**
 * GET /api/receipts/public?token=xxx
 * Public receipt access using secure token (no authentication required)
 * Token is generated during payment return and valid for limited time
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const token = url.searchParams.get('token');

    console.log("Public Receipt API: Starting request for token:", token?.substring(0, 10) + "...");

    if (!token) {
      return new Response("Receipt token is required", { status: 400 });
    }

    // Verify and decode the receipt token
    const receiptData = verifyReceiptToken(token);
    if (!receiptData) {
      return new Response("Invalid or expired receipt token", { status: 401 });
    }

    const { registrationId, transactionId } = receiptData;

    console.log("Public Receipt API: Token verified for registration:", registrationId);

    const supabaseAdmin = getSupabaseAdmin();

    // Fetch registration data with event and transaction details
    const { data: registration, error: regError } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        event:event_id (
          id,
          title,
          slug,
          description_html,
          location,
          start_date,
          end_date,
          image_url,
          price
        ),
        transaction:transaction_id (
          id,
          status,
          amount,
          currency,
          gateway_transaction_id,
          invoice_number,
          receipt_number,
          processed_at,
          created_at
        )
      `)
      .eq("id", registrationId)
      .single();

    if (regError || !registration) {
      console.error("Public Receipt API: Registration not found:", regError);
      return new Response("Registration not found", { status: 404 });
    }

    // Verify transaction matches
    if (registration.transaction_id !== transactionId) {
      console.error("Public Receipt API: Transaction mismatch");
      return new Response("Invalid receipt token", { status: 401 });
    }

    // Check if this is a free event
    const isFreeEvent = !registration.event?.price || registration.event.price === 0;

    // For free events, allow receipt access if registration is confirmed
    if (isFreeEvent) {
      if (registration.payment_status !== 'paid' && registration.status !== 'confirmed') {
        return new Response("Receipt not available - registration not confirmed", { status: 400 });
      }
    } else {
      // For paid events, only allow receipts for paid transactions
      if (registration.transaction?.status !== 'paid') {
        return new Response("Receipt not available - payment not completed", { status: 400 });
      }
    }

    // Generate receipt HTML
    const event = registration.event;
    const transaction = registration.transaction;

    const eventDate = new Date(event?.start_date || "").toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const receiptHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt - ${event?.title || "Unknown Event"}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 10px;
            background: white;
            color: #333;
            line-height: 1.4;
        }

        .receipt {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .content {
            padding: 25px;
        }

        .section {
            margin-bottom: 20px;
        }

        .section h2 {
            font-size: 18px;
            color: #667eea;
            margin-bottom: 12px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 5px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            font-size: 14px;
        }

        .detail-row:nth-child(even) {
            background: #f8fafc;
            margin: 0 -15px;
            padding: 5px 15px;
        }

        .label {
            font-weight: bold;
            color: #4a5568;
        }

        .value {
            color: #2d3748;
        }

        .status-paid {
            color: #10b981;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            font-size: 12px;
        }

        @media print {
            body {
                background: white;
            }
            .receipt {
                border: none;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <h1>mTicket.my</h1>
            <p>Payment Receipt</p>
            <p style="font-size: 16px; margin-top: 10px;">${event?.title || "Unknown Event"}</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>Receipt Details</h2>
                <div class="detail-row">
                    <span class="label">Receipt Number:</span>
                    <span class="value">${transaction?.receipt_number || 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Invoice Number:</span>
                    <span class="value">${transaction?.invoice_number || 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Transaction ID:</span>
                    <span class="value">${transaction?.gateway_transaction_id || 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Payment Date:</span>
                    <span class="value">${new Date(registration.payment_date || registration.created_at).toLocaleDateString()}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Status:</span>
                    <span class="value status-paid">PAID</span>
                </div>
            </div>

            <div class="section">
                <h2>Event Details</h2>
                <div class="detail-row">
                    <span class="label">Event:</span>
                    <span class="value">${event?.title || "Unknown Event"}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Date:</span>
                    <span class="value">${eventDate}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Location:</span>
                    <span class="value">${event?.location || "TBA"}</span>
                </div>
            </div>

            <div class="section">
                <h2>Participant Details</h2>
                <div class="detail-row">
                    <span class="label">Name:</span>
                    <span class="value">${registration.attendee_name}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Email:</span>
                    <span class="value">${registration.attendee_email}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Amount Paid:</span>
                    <span class="value">RM ${parseFloat(registration.payment_amount || '0').toFixed(2)}</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p style="margin: 0 0 8px 0; font-weight: bold;">Thank you for your registration!</p>
            <p style="margin: 0 0 5px 0;">This is your official payment receipt.</p>
            <p style="font-size: 10px; color: #64748b; margin: 0;">
                Official receipt from mTicket.my | Generated: ${new Date().toLocaleString()}
            </p>
        </div>
    </div>

    <script>
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        }
    </script>
</body>
</html>
    `;

    return new Response(receiptHtml, {
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    return new Response("Internal server error", { status: 500 });
  }
}

/**
 * Generate a secure receipt token
 */
export function generateReceiptToken(registrationId: string, transactionId: string): string {
  const payload = {
    registrationId,
    transactionId,
    timestamp: Date.now(),
    expires: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
  };

  const secret = process.env.JWT_SECRET || "your-secret-key";
  const data = JSON.stringify(payload);
  const signature = crypto.createHmac('sha256', secret).update(data).digest('hex');

  return Buffer.from(JSON.stringify({ data, signature })).toString('base64');
}

/**
 * Verify and decode a receipt token
 */
function verifyReceiptToken(token: string): { registrationId: string; transactionId: string } | null {
  try {
    const decoded = JSON.parse(Buffer.from(token, 'base64').toString());
    const { data, signature } = decoded;

    const secret = process.env.JWT_SECRET || "your-secret-key";
    const expectedSignature = crypto.createHmac('sha256', secret).update(data).digest('hex');

    if (signature !== expectedSignature) {
      return null;
    }

    const payload = JSON.parse(data);

    // Check if token has expired
    if (Date.now() > payload.expires) {
      return null;
    }

    return {
      registrationId: payload.registrationId,
      transactionId: payload.transactionId
    };
  } catch (error) {
    return null;
  }
}
