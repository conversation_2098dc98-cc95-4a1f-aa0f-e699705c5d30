"use client"

import { createContext, useContext, useState, type ReactNode } from "react"
import { v4 as uuidv4 } from "uuid"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

// Types
export type ParticipantType = {
  name: string
  ic: string
  phone: string
  email: string
}

export type RegistrationType = {
  id: string
  event_id: string
  user_id: string
  created_by?: string // For group registrations
  registration_date: string
  status: "pending" | "confirmed" | "cancelled"
  payment_status: "pending" | "processing" | "confirmed" | "failed"
  participants: ParticipantType[]
}

export type PaymentType = {
  id: string
  registration_id: string
  amount: number
  currency: string
  payment_method: string
  payment_gateway_id?: string | null
  status: "pending" | "processing" | "confirmed" | "failed"
  transaction_id: string | null
  payment_date: string | null
}

type RegistrationContextType = {
  registrations: RegistrationType[]
  loading: boolean
  error: string | null
  registerForEvent: (
    eventId: string,
    userId: string,
    participants: ParticipantType[],
  ) => Promise<RegistrationType | null>
  cancelRegistration: (id: string) => Promise<boolean>
  processPayment: (registrationId: string, amount: number, paymentMethod: string, paymentGatewayId?: string) => Promise<PaymentType | null>
  getUserRegistrations: (userId: string) => Promise<RegistrationType[]>
}

const RegistrationContext = createContext<RegistrationContextType | undefined>(undefined)

export function RegistrationProvider({ children }: { children: ReactNode }) {
  const [registrations, setRegistrations] = useState<RegistrationType[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Register for an event
  const registerForEvent = async (
    eventId: string,
    userId: string,
    participants: ParticipantType[],
  ): Promise<RegistrationType | null> => {
    setLoading(true)
    try {
      // Log activity
      await logActivity({
        action: "registration_started",
        user_id: userId,
        details: { event_id: eventId, participant_count: participants.length },
      })

      const registration = {
        id: uuidv4(),
        event_id: eventId,
        user_id: userId,
        created_by: userId, // Set created_by for group registration support
        registration_date: new Date().toISOString(),
        status: "pending",
        payment_status: "pending",
        participants,
      } as RegistrationType

      const { data, error } = await supabase.from("registrations").insert([registration]).select()

      if (error) throw error

      const newRegistration = data[0] as RegistrationType
      setRegistrations((prev) => [...prev, newRegistration])

      // Log activity
      await logActivity({
        action: "registration_completed",
        user_id: userId,
        details: {
          event_id: eventId,
          registration_id: newRegistration.id,
          participant_count: participants.length,
        },
      })

      toast({
        title: "Success",
        description: "Registration successful",
      })

      return newRegistration
    } catch (err) {
      console.error("Error registering for event: ", err)
      setError("Failed to register for event")
      toast({
        title: "Error",
        description: "Failed to register for event",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Cancel a registration
  const cancelRegistration = async (id: string): Promise<boolean> => {
    setLoading(true)
    try {
      const registrationToCancel = registrations.find((reg) => reg.id === id)

      const { error } = await supabase.from("registrations").update({ status: "cancelled" }).eq("id", id)

      if (error) throw error

      setRegistrations((prev) => prev.map((reg) => (reg.id === id ? { ...reg, status: "cancelled" } : reg)))

      // Log activity
      if (registrationToCancel) {
        await logActivity({
          action: "registration_cancelled",
          user_id: registrationToCancel.user_id,
          details: {
            registration_id: id,
            event_id: registrationToCancel.event_id,
          },
        })
      }

      toast({
        title: "Success",
        description: "Registration cancelled successfully",
      })

      return true
    } catch (err) {
      console.error("Error cancelling registration: ", err)
      setError("Failed to cancel registration")
      toast({
        title: "Error",
        description: "Failed to cancel registration",
        variant: "destructive",
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // Process payment for a registration
  const processPayment = async (
    registrationId: string,
    amount: number,
    paymentMethod: string,
    paymentGatewayId?: string,
  ): Promise<PaymentType | null> => {
    setLoading(true)
    try {
      const registration = registrations.find((reg) => reg.id === registrationId)

      if (!registration) {
        throw new Error("Registration not found")
      }

      // Log activity
      await logActivity({
        action: "payment_started",
        user_id: registration.user_id,
        details: {
          registration_id: registrationId,
          amount,
          payment_method: paymentMethod,
          payment_gateway_id: paymentGatewayId,
        },
      })

      // Create payment record
      const payment = {
        id: uuidv4(),
        registration_id: registrationId,
        amount,
        currency: "MYR",
        payment_method: paymentMethod,
        payment_gateway_id: paymentGatewayId || null,
        status: "processing",
        transaction_id: null,
        payment_date: null,
      } as PaymentType

      const { data: paymentData, error: paymentError } = await supabase.from("payments").insert([payment]).select()

      if (paymentError) throw paymentError

      const newPayment = paymentData[0] as PaymentType

      // Update registration with payment status
      const { error: updateError } = await supabase
        .from("registrations")
        .update({ payment_status: "processing" })
        .eq("id", registrationId)

      if (updateError) throw updateError

      // Simulate payment processing
      // In a real app, this would redirect to a payment gateway
      setTimeout(async () => {
        try {
          // Update payment status to confirmed
          const { error: confirmError } = await supabase
            .from("payments")
            .update({
              status: "confirmed",
              transaction_id: `TXN-${uuidv4().slice(0, 8)}`,
              payment_date: new Date().toISOString(),
            })
            .eq("id", newPayment.id)

          if (confirmError) throw confirmError

          // Update registration payment status
          const { error: regUpdateError } = await supabase
            .from("registrations")
            .update({ payment_status: "confirmed", status: "confirmed" })
            .eq("id", registrationId)

          if (regUpdateError) throw regUpdateError

          // Log activity
          await logActivity({
            action: "payment_completed",
            user_id: registration.user_id,
            details: {
              registration_id: registrationId,
              payment_id: newPayment.id,
              amount,
              payment_gateway_id: newPayment.payment_gateway_id,
            },
          })

          toast({
            title: "Success",
            description: "Payment processed successfully",
          })
        } catch (err) {
          console.error("Error confirming payment: ", err)

          // Log activity
          await logActivity({
            action: "payment_failed",
            user_id: registration.user_id,
            details: {
              registration_id: registrationId,
              payment_id: newPayment.id,
              payment_gateway_id: newPayment.payment_gateway_id,
              error: (err as Error).message,
            },
          })

          toast({
            title: "Error",
            description: "Failed to process payment",
            variant: "destructive",
          })
        }
      }, 3000) // Simulate 3-second payment processing

      return newPayment
    } catch (err) {
      console.error("Error processing payment: ", err)
      setError("Failed to process payment")
      toast({
        title: "Error",
        description: "Failed to process payment",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Get user registrations
  const getUserRegistrations = async (userId: string): Promise<RegistrationType[]> => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("registrations")
        .select("*")
        .eq("user_id", userId)
        .order("registration_date", { ascending: false })

      if (error) throw error

      return data as RegistrationType[]
    } catch (err) {
      console.error("Error fetching user registrations: ", err)
      setError("Failed to fetch registrations")
      return []
    } finally {
      setLoading(false)
    }
  }

  // Log activity utility function
  const logActivity = async ({
    action,
    user_id,
    details,
  }: {
    action: string
    user_id: string
    details: Record<string, any>
  }) => {
    try {
      await supabase.from("activity_logs").insert([
        {
          action,
          user_id,
          entity_type: "registration",
          category: "registration",
          details,
          created_at: new Date().toISOString(),
        },
      ])
    } catch (err) {
      console.error("Error logging activity: ", err)
    }
  }

  const value = {
    registrations,
    loading,
    error,
    registerForEvent,
    cancelRegistration,
    processPayment,
    getUserRegistrations,
  }

  return <RegistrationContext.Provider value={value}>{children}</RegistrationContext.Provider>
}

export function useRegistrations() {
  const context = useContext(RegistrationContext)
  if (context === undefined) {
    throw new Error("useRegistrations must be used within a RegistrationProvider")
  }
  return context
}
