#!/usr/bin/env node

/**
 * Comprehensive Event Management System Test
 * Tests create, edit, view, and list functionality
 */

const fs = require('fs');
const path = require('path');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_IMAGE_PATH = path.join(__dirname, 'test-image.jpg');

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, passed, message = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  const result = `${status}: ${name}${message ? ' - ' + message : ''}`;
  console.log(result);
  
  testResults.tests.push({ name, passed, message });
  if (passed) testResults.passed++;
  else testResults.failed++;
}

function logSection(title) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🧪 ${title}`);
  console.log('='.repeat(60));
}

async function createTestImage() {
  // Create a simple test image (1x1 pixel PNG)
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
    0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x0F, 0x00, 0x00,
    0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0xDB, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);
  
  fs.writeFileSync(TEST_IMAGE_PATH, pngData);
  return TEST_IMAGE_PATH;
}

async function testPageLoad(url, expectedTitle = null) {
  try {
    const response = await fetch(url);
    const isSuccess = response.ok;
    
    if (expectedTitle) {
      const html = await response.text();
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      const actualTitle = titleMatch ? titleMatch[1] : '';
      const titleMatches = actualTitle.includes(expectedTitle);
      
      return {
        success: isSuccess && titleMatches,
        status: response.status,
        title: actualTitle,
        message: `Status: ${response.status}, Title: "${actualTitle}"`
      };
    }
    
    return {
      success: isSuccess,
      status: response.status,
      message: `Status: ${response.status}`
    };
  } catch (error) {
    return {
      success: false,
      message: `Error: ${error.message}`
    };
  }
}

async function testEventAPI() {
  try {
    const response = await fetch(`${BASE_URL}/api/events`);
    const data = await response.json();
    
    return {
      success: response.ok && Array.isArray(data.events),
      eventCount: data.events ? data.events.length : 0,
      message: `Found ${data.events ? data.events.length : 0} events`
    };
  } catch (error) {
    return {
      success: false,
      message: `API Error: ${error.message}`
    };
  }
}

async function testDashboardAPI() {
  try {
    // This would require authentication, so we'll just test if the endpoint exists
    const response = await fetch(`${BASE_URL}/api/dashboard/events`);
    
    return {
      success: response.status !== 404,
      status: response.status,
      message: `Dashboard API Status: ${response.status}`
    };
  } catch (error) {
    return {
      success: false,
      message: `Dashboard API Error: ${error.message}`
    };
  }
}

async function runTests() {
  console.log('🚀 Starting Event Management System Tests');
  console.log(`Base URL: ${BASE_URL}`);
  
  // Test 1: Page Load Tests
  logSection('PAGE LOAD TESTS');
  
  const homeTest = await testPageLoad(`${BASE_URL}/`);
  logTest('Home Page Load', homeTest.success, homeTest.message);
  
  const eventsTest = await testPageLoad(`${BASE_URL}/events`);
  logTest('Events Page Load', eventsTest.success, eventsTest.message);
  
  const createTest = await testPageLoad(`${BASE_URL}/dashboard/events/create`);
  logTest('Create Event Page Load', createTest.success, createTest.message);
  
  const dashboardTest = await testPageLoad(`${BASE_URL}/dashboard/events`);
  logTest('Dashboard Events Page Load', dashboardTest.success, dashboardTest.message);
  
  // Test 2: API Tests
  logSection('API TESTS');
  
  const apiTest = await testEventAPI();
  logTest('Events API', apiTest.success, apiTest.message);
  
  const dashboardApiTest = await testDashboardAPI();
  logTest('Dashboard API Endpoint', dashboardApiTest.success, dashboardApiTest.message);
  
  // Test 3: Image Upload Preparation
  logSection('IMAGE UPLOAD TESTS');
  
  try {
    await createTestImage();
    logTest('Test Image Creation', true, 'Test image created successfully');
  } catch (error) {
    logTest('Test Image Creation', false, `Error: ${error.message}`);
  }
  
  // Test 4: Form Validation Tests (Client-side)
  logSection('FORM VALIDATION TESTS');
  
  // These would be tested in the browser, but we can verify the form schema exists
  try {
    const formComponentPath = path.join(__dirname, 'components', 'event-form.tsx');
    const formExists = fs.existsSync(formComponentPath);
    logTest('Event Form Component Exists', formExists, formExists ? 'Found event-form.tsx' : 'event-form.tsx not found');
  } catch (error) {
    logTest('Event Form Component Check', false, `Error: ${error.message}`);
  }
  
  // Test 5: Image Compression Library
  logSection('IMAGE COMPRESSION TESTS');
  
  try {
    const compressionLibPath = path.join(__dirname, 'lib', 'image-compression.ts');
    const compressionExists = fs.existsSync(compressionLibPath);
    logTest('Image Compression Library Exists', compressionExists, compressionExists ? 'Found image-compression.ts' : 'image-compression.ts not found');
  } catch (error) {
    logTest('Image Compression Library Check', false, `Error: ${error.message}`);
  }
  
  // Test 6: Database Schema Validation
  logSection('DATABASE SCHEMA TESTS');
  
  try {
    const migrationDir = path.join(__dirname, 'supabase', 'migrations');
    const migrationsExist = fs.existsSync(migrationDir);
    logTest('Migration Directory Exists', migrationsExist, migrationsExist ? 'Found migrations directory' : 'Migrations directory not found');
    
    if (migrationsExist) {
      const migrationFiles = fs.readdirSync(migrationDir).filter(f => f.endsWith('.sql'));
      logTest('Migration Files Found', migrationFiles.length > 0, `Found ${migrationFiles.length} migration files`);
    }
  } catch (error) {
    logTest('Database Schema Check', false, `Error: ${error.message}`);
  }
  
  // Final Results
  logSection('TEST RESULTS SUMMARY');
  
  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log(`\n🔍 Failed Tests:`);
    testResults.tests.filter(t => !t.passed).forEach(test => {
      console.log(`   • ${test.name}: ${test.message}`);
    });
  }
  
  // Cleanup
  try {
    if (fs.existsSync(TEST_IMAGE_PATH)) {
      fs.unlinkSync(TEST_IMAGE_PATH);
    }
  } catch (error) {
    console.log(`⚠️  Warning: Could not clean up test image: ${error.message}`);
  }
  
  console.log(`\n🎯 Next Steps:`);
  console.log(`1. Open browser to ${BASE_URL}/dashboard/events/create`);
  console.log(`2. Test event creation with image upload`);
  console.log(`3. Test event editing functionality`);
  console.log(`4. Test event viewing and listing`);
  console.log(`5. Verify all form validations work correctly`);
  
  return testResults.failed === 0;
}

// Run tests if called directly
if (require.main === module) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests, testPageLoad, testEventAPI };
