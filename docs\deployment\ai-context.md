# AI Assistant Memory - mTicket.my Project

This document serves as a comprehensive memory bank for the AI assistant working on the mTicket.my project. It contains all the key information, decisions, and current state of the application.

## 📋 Project Overview

**Project Name**: mTicket.my
**Type**: Comprehensive Event Management Platform
**Technology Stack**: Next.js 15, React 18, TypeScript, Supabase, Tailwind CSS
**Current Status**: Production-ready with comprehensive features and advanced security

## 🔐 Authentication & Security

### Authentication System
- **Migration Status**: Successfully migrated from Supabase Auth to custom JWT implementation
- **Database Storage**: Using Supabase PostgreSQL with `users` table containing `password_hash` and `role_id` columns
- **Session Management**: Custom JWT implementation with secure token management
- **Token Storage**: Added `auth_token`, `auth_token_expiry`, `reset_token`, `reset_token_expiry` columns to users table
- **Profile Enhancement**: Added `phone` and `bio` fields to user profiles
- **Security Resolution**: Implemented proper JWT handling for Next.js 15 Edge runtime compatibility

### Row Level Security (RLS)
- **Status**: ✅ COMPLETE - All 24 tables have RLS enabled with comprehensive policies
- **Recent Achievement**: Removed duplicate `user_subscriptions` table, enabled RLS on all remaining tables
- **Policy Coverage**: 4 policies per table (SELECT, INSERT, UPDATE, DELETE) with role-based access control
- **Security Patterns**: User ownership, role-based access, event management boundaries, public data access, admin overrides

### Advanced Ticket Security
- **✅ Dynamic QR Codes**: Time-based tokens that expire every 30 seconds for 6 cycles
- **HMAC Signatures**: Cryptographic signatures prevent QR code forgery using SHA-256
- **Replay Protection**: Unique nonces prevent ticket reuse and replay attacks
- **Cycle Management**: Automatic refresh for 6 cycles, then manual refresh required
- **Check-in Control**: QR codes stop updating once user is checked in
- **Time Window Validation**: Server-side time synchronization with 5-second grace periods
- **Secure Token Generation**: Cryptographically secure random token generation with base64url encoding
- **API Endpoints**: `/api/tickets/secure-qr` for generation, `/api/verify/secure` for verification

## 👥 Role-Based Access Control

### Role System (5 Roles in Database)
- **Implementation**: Granular role-based access control using `user_roles` table with `role_name` column
- **Database Roles**: Exactly 5 roles defined:
  1. **admin** - Full system administrator with access to all systems
  2. **user** - Registered users/participants
  3. **manager** - Users who created organizations and can manage their own events and participants
  4. **supermanager** - Same as manager but with commission access (future feature)
  5. **event_admin** - Can manage all events created in the system (not just their own)
- **Access Control**: Only "admin" role has full access to all functions, other admin-type roles have elevated permissions but not full system access
- **Admin Features**: Admin menu only visible to users with "admin" role specifically, instant role updates without refresh needed
- **User Management**: Admin page to manage all roles, filtering by role functionality, reset password actions

### User Management Features
- **Search-Based Filtering**: Preferred over dropdown selection for large user numbers
- **Data Fetching Pattern**: Client-side fetch to API endpoints for data retrieval, server actions for mutations
- **View Details**: Shows all appropriate columns from user table
- **Reset Password**: Admin action icon for direct password changes or reset emails
- **Profile Management**: Profile picture uploads with image compression to save storage space

## 🏢 Organization Management

### Organization Features
- **✅ Comprehensive CRUD**: Full Create, Read, Update, Delete functionality for organizations
- **✅ Advanced Search & Link**: Searchable dropdown with real-time search to select existing organizations or create new ones
- **✅ Permission-Based Editing**: Only organization creator, admin, or manager roles can edit organization details
- **✅ Visual Status Indicators**: Clear "Linked" vs "Selected" status with green badges for linked organizations
- **✅ Safe Unlink Functionality**: Removes organization from user profile without deleting the organization
- **✅ Duplicate Prevention**: Checks for existing organizations by name and SSM number before creation
- **✅ Profile Integration**: Seamlessly integrated into profile page tabs for better user experience

### Organization Operations
1. **✅ Create New Organization**: Full organization creation with SSM number, PIC details, address, website
2. **✅ Link Existing Organization**: Search and link to existing organizations in database with real-time search
3. **✅ Edit Linked Organization**: Update organization details with proper permission validation
4. **✅ Unlink Organization**: Remove organization from user profile while preserving organization data
5. **✅ Search Organizations**: Real-time search with debouncing and limit controls

### UI/UX Features
- **✅ Real-time Search**: Organization search with debouncing for optimal performance
- **✅ Contextual Buttons**: Dynamic button states - "Link Organization", "Update Organization", "Already Linked", "Create & Link"
- **✅ Status Display**: Clear visual indicators - "Linked: [Name]" with green badge vs "Selected: [Name]" without badge
- **✅ Profile Tab Integration**: Organization management integrated into profile page tabs for maintainability
- **✅ Command Interface**: Uses shadcn/ui Command component for better search UX

### API Endpoints
- **✅ POST /api/organizations/create**: Create new organization with authentication and role assignment
- **✅ PUT /api/organizations/update**: Update existing organization with permission checks and duplicate prevention
- **✅ GET /api/organizations**: Public search endpoint for organization lookup with search and limit parameters
- **✅ POST /api/organizations/link**: Link user to existing organization and upgrade to manager role
- **✅ Activity Logging**: All organization operations are logged for audit trails

## 🎫 Event Management

### Event Structure
- **Database Fields**: Uses `start_date` and `end_date` columns (NOT start_datetime/end_datetime)
- **Event Slugs**: Random selection from [0-9, a-z, A-Z] starting from 4-5 characters
- **URL Pattern**: Alternative short URL pattern `/slug` instead of `/events/slug` for QR codes
- **Organizer Info**: Events display organizer from `organizations` table using `organization_id`, not from `users` table
- **Main Page**: Tabs for latest/featured/popular events with featured as center tab
- **Category System**: Category filtering redirects to `/events?category=` with proper badge display

### Event Display
- **Organizer Links**: Organizer names are clickable links that filter to show all active events by that organizer
- **Category Badges**: Positioned at right bottom of event card images with purple color matching app scheme
- **Action Buttons**: Subscribe/action buttons positioned at bottom of cards for consistent alignment
- **Background**: All main pages have same purple background top section as main page

## 💳 Subscription & Payment System

### Subscription Management
- **Database Storage**: Subscription plans stored in database (`subscription_plans` table) with editable features
- **Workflow**: Register → Pay → Update `user_subscription` and `users` tables
- **Integration**: Integrated with `payment_gateway_settings`
- **Auto-Generation**: Plans auto-generate features when enabled (max events, attendees, certificates, attendances)
- **URL Convention**: Uses `/pricing` path instead of `/subscriptions` following standard naming

### Current Table Structure
- **Active Table**: `user_subscription` (contains data)
- **Removed Table**: `user_subscriptions` (was duplicate and empty)
- **Features**: Plans include certificates_enabled, attendances_enabled, webhooks_enabled, analytics_enabled, reports_enabled

## 🏆 Certificate System

### Certificate Management
- **QR Codes**: Certificates display QR codes for authenticity verification
- **Template Editor**: Drag-drop field repositioning, supports both image upload and custom HTML design
- **Default Templates**: System provides default certificate templates with customizable logos and fields
- **Verification**: Existing certificate verification page at `app/certificates/verify/page.tsx`
- **Management**: Sidebar menu 'Manage certificates' under `dashboard/certificates/templates`
- **Orientation**: Template editor supports both landscape and portrait mode selection

## 🎨 UI & UX Enhancements

### Brand & Navigation
- **✅ Brand Name**: Changed from mTicketz to mTicket.my throughout application
- **✅ Navigation Order**: Home Events About Pricing Contact
- **✅ Page Titles**: Format '<page> | mTicket.my - Event Management Platform'
- **✅ Sticky Sidebar**: Implemented when scrolling with hover tooltips for action buttons

### Visual Design
- **✅ Background**: All main pages have purple background top section matching main page
- **✅ Event Cards**: Category badges at right bottom, organizer names as clickable filter links
- **✅ Color Scheme**: Purple color matching application scheme for category badges and UI elements
- **✅ Dialog Layouts**: Wider dialog layouts preferred over tall ones for better UX
- **✅ Ticket Dialog**: Equal height sections for attendee information and ticket status
- **✅ Compact UI**: More compact layouts that don't require scrolling

### Profile Page Enhancements
- **✅ Tabbed Interface**: Profile, Organization, Subscription, Notifications, API & Webhooks tabs
- **✅ Modular Components**: Refactored into separate tab components for maintainability
- **✅ Image Compression**: Profile picture uploads include automatic image compression
- **✅ Organization Integration**: Comprehensive organization management within profile

## 📊 System Features

### Activity Logging
- **✅ Implementation**: Comprehensive activity logging for all user actions
- **✅ Filtering**: Includes filtering by user functionality
- **✅ Coverage**: Tracks all mutations and important user interactions
- **✅ Organization Tracking**: All organization operations are logged for audit trails

### Webhook System
- **✅ Implementation**: User-specific token creation for API security
- **✅ Integration**: Part of subscription plan features (webhooks_enabled)
- **✅ Profile Integration**: Moved webhook management to profile tab for better UX

### Analytics & Monitoring
- **✅ Vercel Integration**: Vercel Analytics and Speed Insights components integrated
- **✅ Performance**: Monitoring and performance tracking implemented

### Dynamic QR Code System
- **✅ Component**: `DynamicQRCode` component with time-based token refresh
- **✅ Security Features**: HMAC signatures, nonce protection, cycle management
- **✅ User Experience**: Timer display, security badges, check-in status
- **✅ API Integration**: Secure QR generation and verification endpoints

## 🛠️ Development Practices

### Package Management
- **Preferred Tool**: pnpm instead of npm for package installation
- **Policy**: Always use package managers instead of manually editing package files

### Documentation System
- **Location**: Comprehensive documentation in `docs/` directory
- **Purpose**: Single source of truth to avoid duplication
- **Coverage**: All routes, API endpoints, components, database schema, RLS implementation
- **Files**: Master index, application structure, API reference, components reference, database schema, RLS summary

### Data Fetching Patterns
- **Client-Side**: Use client-side fetch to API endpoints for data retrieval
- **Server Actions**: Use server actions for data mutations (create, update, delete)
- **Granular Documentation**: Shows detailed information on each function and server-side data fetching

## 🗄️ Database Status

### Current Tables (24 total)
All tables have RLS enabled with comprehensive policies:
- activity_logs, attendance_settings, auth_sessions, certificate_templates
- certificates, event_categories, events, transactions
- organizations, participants, payment_gateway_settings, race_results
- registrations, reports, subscription_analytics, subscription_plans
- system_settings, ticket_types, user_roles, user_sessions
- user_subscription, users, webinar_sessions, workshop_sessions

### Recent Database Changes
- **Cleanup**: Removed duplicate `user_subscriptions` table
- **RLS**: Enabled on all 24 tables with 4 policies each (where appropriate)
- **Security**: Complete role-based access control implementation

## 📝 Recent Major Updates

### Documentation
- **README.md**: Completely updated to reflect current comprehensive platform state
- **RLS Documentation**: Created comprehensive RLS implementation summary
- **Database Schema**: Updated with current table structure and RLS status

### Security Implementation
- **RLS Completion**: All 24 tables now have Row Level Security enabled
- **Policy Creation**: Comprehensive policies for role-based access control
- **Database Cleanup**: Removed duplicates and ensured data integrity

This memory document should be updated whenever significant changes are made to the project.
