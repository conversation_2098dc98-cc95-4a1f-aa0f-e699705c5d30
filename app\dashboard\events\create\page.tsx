"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft } from "lucide-react"

import { Button } from "@/components/ui/button"
import { EventForm } from "@/components/event-form"
import type { EventType } from "@/contexts/event-context"
import { useAuth } from "@/contexts/auth-context"

export default function CreateEventPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSuccess = (event: EventType) => {
    setIsSubmitting(false)
    router.push(`/dashboard/events/${event.slug}`)
  }

  if (!user) {
    return (
      <div className="w-full max-w-6xl px-4 py-6 mx-auto">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Authentication Required</h2>
          <p className="text-muted-foreground mb-6">Please log in to create an event.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-6xl px-4 py-6 mx-auto">
      <div className="mb-6 flex items-center">
        <Button variant="ghost" size="sm" className="mr-4" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Create New Event</h1>
      </div>

      <div className="rounded-lg border bg-card p-4 sm:p-6 w-full">
        <EventForm userId={user.id} onSuccess={handleSuccess} />
      </div>
    </div>
  )
}
