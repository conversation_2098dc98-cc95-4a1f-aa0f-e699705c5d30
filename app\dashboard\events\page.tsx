"use client"

import React, { use<PERSON>tate, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import {
  Plus,
  Filter,
  Eye,
  Edit,
  Users,
  FileText,
  Archive,
  Trash,
  CheckCircle,
  MoreHorizontal,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/contexts/auth-context"
import { seedEvents } from "@/utils/seed-data"

import type { ToastProps } from "@/components/ui/toast"

type ToastFunction = (props: {
  title: string;
  description: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning' | null | undefined;
}) => void;

export default function EventsPage() {
  const router = useRouter()
  const { toast } = useToast() as { toast: ToastFunction }
  const { user, loading: authLoading, isAdmin } = useAuth()
  const [events, setEvents] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedEvent, setSelectedEvent] = useState<any>(null)
  const [isArchiveDialogOpen, setIsArchiveDialogOpen] = useState(false)
  const [needsSeeding, setNeedsSeeding] = useState(false)
  const [isAdminView, setIsAdminView] = useState(false)
  const [managerFilter, setManagerFilter] = useState<string | null>(null)
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null)

  // Helper function to get auth token from cookies
  const getAuthToken = (): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; auth_token=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      try {
        // Get auth token
        const token = getAuthToken();
        if (!token) {
          throw new Error("No authentication token found");
        }

        // Fetch data from API endpoint
        const response = await fetch('/api/dashboard/events', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            throw new Error("Authentication failed. Please log in again.");
          }
          throw new Error(`Failed to fetch events: ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        const eventsData = data.events || [];
        setEvents(eventsData);

        // Set admin view state based on API response
        setIsAdminView(data.isAdmin || false);

        // Show seeding option if no events exist
        setNeedsSeeding(eventsData.length === 0);

      } catch (error: any) {
        console.error("Error fetching events:", error);
        toast({
          title: "Error",
          description: error.message || "Failed to fetch events. Please try again later.",
          variant: "destructive",
        });

        // If authentication failed, clear events
        if (error.message.includes("Authentication")) {
          setEvents([]);
        }
      } finally {
        setLoading(false);
      }
    };

    // Only fetch events if user is authenticated and not loading
    if (!authLoading && user) {
      fetchEvents();
    } else if (!authLoading && !user) {
      setLoading(false);
      setEvents([]);
    }
  }, [user, authLoading, toast])

  const handleSeedEvents = async () => {
    setLoading(true);
    try {
      const success = await seedEvents();
      if (success) {
        toast({
          title: "Success",
          description: "Demo events created successfully!",
          variant: "default"
        });
        // Refresh the page to show the new events
        window.location.reload()
      } else {
        throw new Error("Failed to seed events")
      }
    } catch (error) {
      console.error("Error seeding events:", error)
      toast({
        title: "Error",
        description: "Failed to create demo events. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleArchive = async () => {
    if (!selectedEvent) return

    try {
      // Archive by unpublishing the event
      const { error } = await supabase
        .from("events")
        .update({ is_published: false })
        .eq("id", selectedEvent.id)

      if (error) throw error

      // Update local state - mark as cancelled/archived
      const updatedEvent = { ...selectedEvent, status: "cancelled", is_published: false }
      setEvents(
        events.map((event) => {
          if (event.id === selectedEvent.id) {
            return updatedEvent
          }
          return event
        }),
      )

      // Log the event archive activity
      try {
        await supabase.from("activity_logs").insert([
          {
            user_id: user?.id,
            action: "archive_event",
            entity_type: "event",
            entity_id: selectedEvent.id,
            category: "event",
            details: {
              event_name: selectedEvent.title,
              previous_status: selectedEvent.status,
              new_status: "cancelled",
            },
            created_at: new Date().toISOString(),
          },
        ])
      } catch (logError) {
        // Don't fail event archiving if logging fails
        console.error("Error logging event archive activity:", logError)
      }

      toast({
        title: "Success",
        description: "Event archived successfully",
      })
    } catch (error) {
      console.error("Error archiving event:", error)
      toast({
        title: "Error",
        description: "Failed to archive event",
        variant: "destructive",
      })
    } finally {
      setIsArchiveDialogOpen(false)
      setSelectedEvent(null)
    }
  }

  // Sorting function
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Filter and sort events
  const filteredAndSortedEvents = React.useMemo(() => {
    let filtered = events.filter((event) => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = (
          event.title?.toLowerCase().includes(query) ||
          event.description?.toLowerCase().includes(query) ||
          event.location?.toLowerCase().includes(query) ||
          event.manager?.email?.toLowerCase().includes(query) ||
          event.manager?.full_name?.toLowerCase().includes(query)
        );
        if (!matchesSearch) return false;
      }

      // Manager filter
      if (managerFilter) {
        return event.manager?.email === managerFilter;
      }

      return true;
    });

    // Sort events
    if (sortConfig) {
      filtered.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];

        // Handle nested manager properties
        if (sortConfig.key === 'manager_email') {
          aValue = a.manager?.email || '';
          bValue = b.manager?.email || '';
        } else if (sortConfig.key === 'manager_name') {
          aValue = a.manager?.full_name || '';
          bValue = b.manager?.full_name || '';
        }

        // Handle dates
        if (sortConfig.key === 'start_date' || sortConfig.key === 'end_date') {
          aValue = new Date(aValue);
          bValue = new Date(bValue);
        }

        // Handle numbers
        if (sortConfig.key === 'current_participants') {
          aValue = Number(aValue) || 0;
          bValue = Number(bValue) || 0;
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [events, searchQuery, managerFilter, sortConfig]);

  // Handle manager filter click
  const handleManagerFilter = (email: string) => {
    if (managerFilter === email) {
      setManagerFilter(null); // Clear filter if clicking same manager
    } else {
      setManagerFilter(email);
    }
  };

  // Calculate event counts for each tab
  const getEventCounts = () => {
    const counts = {
      all: filteredAndSortedEvents.length,
      upcoming: filteredAndSortedEvents.filter(
        (event) => !isPastEvent(event.end_date) && event.status !== "draft" && event.status !== "cancelled"
      ).length,
      past: filteredAndSortedEvents.filter(
        (event) => isPastEvent(event.end_date) && event.status !== "cancelled"
      ).length,
      draft: filteredAndSortedEvents.filter((event) => event.status === "draft").length,
      cancelled: filteredAndSortedEvents.filter((event) => event.status === "cancelled").length,
    };
    return counts;
  };

  const eventCounts = getEventCounts();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString()
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge variant="default">Published</Badge>
      case "draft":
        return <Badge variant="secondary">Draft</Badge>
      case "cancelled":
        return <Badge variant="outline">Cancelled</Badge>
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const isPastEvent = (endDate: string) => {
    return new Date(endDate) < new Date()
  }

  // Sortable table header component
  const SortableHeader = ({ column, children }: { column: string; children: React.ReactNode }) => (
    <TableHead
      className="cursor-pointer hover:bg-muted/50 select-none"
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-1">
        {children}
        {sortConfig?.key === column && (
          <span className="text-xs">
            {sortConfig.direction === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </TableHead>
  );

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8">
        <div className="flex h-40 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Events</h2>
          <p className="text-muted-foreground">
            {isAdminView ? "Manage all events in the system" : "Manage your events and track registrations"}
            {managerFilter && (
              <span className="ml-2 text-sm text-blue-600">
                • Filtered by: {managerFilter}
                <button
                  onClick={() => setManagerFilter(null)}
                  className="ml-1 text-red-500 hover:text-red-700"
                >
                  ✕
                </button>
              </span>
            )}
          </p>
        </div>
        <div className="flex gap-2">
          {needsSeeding && (
            <Button onClick={handleSeedEvents} variant="outline">
              Create Demo Events
            </Button>
          )}
          <Link href="/dashboard/events/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Event
            </Button>
          </Link>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="flex w-full md:w-auto items-center gap-2">
          <Input
            placeholder="Search events..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full md:w-[300px]"
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>All Events</DropdownMenuItem>
              <DropdownMenuItem>Published</DropdownMenuItem>
              <DropdownMenuItem>Draft</DropdownMenuItem>
              <DropdownMenuItem>Cancelled</DropdownMenuItem>
              <DropdownMenuItem>Completed</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">All ({eventCounts.all})</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming ({eventCounts.upcoming})</TabsTrigger>
          <TabsTrigger value="past">Past ({eventCounts.past})</TabsTrigger>
          <TabsTrigger value="draft">Draft ({eventCounts.draft})</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled ({eventCounts.cancelled})</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>All Events</CardTitle>
              <CardDescription>
                {isAdminView ? "All events in the system" : "All your events"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <SortableHeader column="title">Event Name</SortableHeader>
                    <SortableHeader column="start_date">Date</SortableHeader>
                    <SortableHeader column="location">Location</SortableHeader>
                    {isAdminView && <SortableHeader column="manager_email">Manager</SortableHeader>}
                    <SortableHeader column="status">Status</SortableHeader>
                    <SortableHeader column="current_participants">Attendees</SortableHeader>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedEvents.map((event) => (
                    <TableRow
                      key={event.id}
                      className="hover:bg-muted/50 cursor-pointer"
                      onClick={(e) => {
                        // Don't navigate if clicking on action buttons
                        if (!(e.target instanceof HTMLElement) ||
                            e.target.closest('button, a, [role="button"]')) {
                          return;
                        }
                        router.push(`/dashboard/events/${event.slug}`);
                      }}
                    >
                      <TableCell className="font-medium">{event.title}</TableCell>
                      <TableCell>{formatDate(event.start_date)}</TableCell>
                      <TableCell>{event.location}</TableCell>
                      {isAdminView && (
                        <TableCell>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleManagerFilter(event.manager?.email || '');
                            }}
                            className="text-blue-600 hover:text-blue-800 hover:underline text-left"
                          >
                            {event.manager?.email || 'N/A'}
                          </button>
                        </TableCell>
                      )}
                      <TableCell>{getStatusBadge(event.status)}</TableCell>
                      <TableCell>
                        {event.current_participants} / {event.max_participants || "∞"}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}/edit`)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                              <Users className="mr-2 h-4 w-4" />
                              Attendees
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                              <FileText className="mr-2 h-4 w-4" />
                              Certificates
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedEvent(event);
                                setIsArchiveDialogOpen(true);
                              }}
                              className="text-red-600"
                            >
                              <Archive className="mr-2 h-4 w-4" />
                              Archive
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upcoming" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Upcoming Events</CardTitle>
                  <CardDescription>
                    {isAdminView ? "Upcoming events in the system" : "Your upcoming events"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <SortableHeader column="title">Event Name</SortableHeader>
                        <SortableHeader column="start_date">Date</SortableHeader>
                        <SortableHeader column="location">Location</SortableHeader>
                        {isAdminView && <SortableHeader column="manager_email">Manager</SortableHeader>}
                        <SortableHeader column="status">Status</SortableHeader>
                        <SortableHeader column="current_participants">Attendees</SortableHeader>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedEvents
                        .filter(
                          (event) =>
                            !isPastEvent(event.end_date) && event.status !== "draft" && event.status !== "cancelled",
                        )
                        .map((event) => (
                          <TableRow
                            key={event.id}
                            className="hover:bg-muted/50 cursor-pointer"
                            onClick={(e) => {
                              // Don't navigate if clicking on action buttons
                              if (!(e.target instanceof HTMLElement) ||
                                  e.target.closest('button, a, [role="button"]')) {
                                return;
                              }
                              router.push(`/dashboard/events/${event.slug}`);
                            }}
                          >
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{formatDate(event.start_date)}</TableCell>
                            <TableCell>{event.location}</TableCell>
                            {isAdminView && (
                              <TableCell>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleManagerFilter(event.manager?.email || '');
                                  }}
                                  className="text-blue-600 hover:text-blue-800 hover:underline text-left"
                                >
                                  {event.manager?.email || 'N/A'}
                                </button>
                              </TableCell>
                            )}
                            <TableCell>{getStatusBadge(event.status)}</TableCell>
                            <TableCell>
                              {event.current_participants} / {event.max_participants || "∞"}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}/edit`)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Attendees
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Certificates
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedEvent(event);
                                      setIsArchiveDialogOpen(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <Archive className="mr-2 h-4 w-4" />
                                    Archive
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="past" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Past Events</CardTitle>
                  <CardDescription>
                    {isAdminView ? "Past events in the system" : "Your past events"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <SortableHeader column="title">Event Name</SortableHeader>
                        <SortableHeader column="start_date">Date</SortableHeader>
                        <SortableHeader column="location">Location</SortableHeader>
                        {isAdminView && <SortableHeader column="manager_email">Manager</SortableHeader>}
                        <SortableHeader column="status">Status</SortableHeader>
                        <SortableHeader column="current_participants">Attendees</SortableHeader>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedEvents
                        .filter((event) => isPastEvent(event.end_date) && event.status !== "cancelled")
                        .map((event) => (
                          <TableRow
                            key={event.id}
                            className="hover:bg-muted/50 cursor-pointer"
                            onClick={(e) => {
                              // Don't navigate if clicking on action buttons
                              if (!(e.target instanceof HTMLElement) ||
                                  e.target.closest('button, a, [role="button"]')) {
                                return;
                              }
                              router.push(`/dashboard/events/${event.slug}`);
                            }}
                          >
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{formatDate(event.start_date)}</TableCell>
                            <TableCell>{event.location}</TableCell>
                            {isAdminView && (
                              <TableCell>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleManagerFilter(event.manager?.email || '');
                                  }}
                                  className="text-blue-600 hover:text-blue-800 hover:underline text-left"
                                >
                                  {event.manager?.email || 'N/A'}
                                </button>
                              </TableCell>
                            )}
                            <TableCell>{getStatusBadge(event.status)}</TableCell>
                            <TableCell>
                              {event.current_participants} / {event.max_participants || "∞"}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}/edit`)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Attendees
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Certificates
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedEvent(event);
                                      setIsArchiveDialogOpen(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <Archive className="mr-2 h-4 w-4" />
                                    Archive
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="draft" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Draft Events</CardTitle>
                  <CardDescription>
                    {isAdminView ? "Draft events in the system" : "Your draft events"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <SortableHeader column="title">Event Name</SortableHeader>
                        <SortableHeader column="start_date">Date</SortableHeader>
                        <SortableHeader column="location">Location</SortableHeader>
                        {isAdminView && <SortableHeader column="manager_email">Manager</SortableHeader>}
                        <SortableHeader column="status">Status</SortableHeader>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedEvents
                        .filter((event) => event.status === "draft")
                        .map((event) => (
                          <TableRow
                            key={event.id}
                            className="hover:bg-muted/50 cursor-pointer"
                            onClick={(e) => {
                              // Don't navigate if clicking on action buttons
                              if (!(e.target instanceof HTMLElement) ||
                                  e.target.closest('button, a, [role="button"]')) {
                                return;
                              }
                              router.push(`/dashboard/events/${event.slug}`);
                            }}
                          >
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{formatDate(event.start_date)}</TableCell>
                            <TableCell>{event.location}</TableCell>
                            {isAdminView && (
                              <TableCell>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleManagerFilter(event.manager?.email || '');
                                  }}
                                  className="text-blue-600 hover:text-blue-800 hover:underline text-left"
                                >
                                  {event.manager?.email || 'N/A'}
                                </button>
                              </TableCell>
                            )}
                            <TableCell>{getStatusBadge(event.status)}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}/edit`)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Attendees
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Certificates
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => {
                                    // Publish event logic - to be implemented
                                  }}>
                                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                    Publish
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedEvent(event);
                                      setIsArchiveDialogOpen(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <Trash className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cancelled" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Cancelled Events</CardTitle>
                  <CardDescription>
                    {isAdminView ? "Cancelled events in the system" : "Your cancelled events"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <SortableHeader column="title">Event Name</SortableHeader>
                        <SortableHeader column="start_date">Date</SortableHeader>
                        <SortableHeader column="location">Location</SortableHeader>
                        {isAdminView && <SortableHeader column="manager_email">Manager</SortableHeader>}
                        <SortableHeader column="status">Status</SortableHeader>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedEvents
                        .filter((event) => event.status === "cancelled")
                        .map((event) => (
                          <TableRow key={event.id}>
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{formatDate(event.start_date)}</TableCell>
                            <TableCell>{event.location}</TableCell>
                            {isAdminView && (
                              <TableCell>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleManagerFilter(event.manager?.email || '');
                                  }}
                                  className="text-blue-600 hover:text-blue-800 hover:underline text-left"
                                >
                                  {event.manager?.email || 'N/A'}
                                </button>
                              </TableCell>
                            )}
                            <TableCell>{getStatusBadge(event.status)}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Attendees
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Certificates
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
      </Tabs>

      {/* Archive Confirmation Dialog */}
      <Dialog open={isArchiveDialogOpen} onOpenChange={setIsArchiveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Archive Event</DialogTitle>
            <DialogDescription>
              Are you sure you want to archive {selectedEvent?.title}? It will no longer be visible to the public.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsArchiveDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleArchive}>
              Archive
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
