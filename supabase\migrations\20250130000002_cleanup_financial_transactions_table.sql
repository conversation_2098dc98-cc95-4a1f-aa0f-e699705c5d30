-- Cleanup migration: Drop the old financial_transactions table after successful merge
-- This migration removes the redundant financial_transactions table and cleans up references

-- Verify that all data has been migrated successfully
DO $$
DECLARE
  ft_count INTEGER;
  t_migrated_count INTEGER;
BEGIN
  -- Count records in financial_transactions
  SELECT COUNT(*) INTO ft_count FROM financial_transactions;
  
  -- Count migrated records in transactions
  SELECT COUNT(*) INTO t_migrated_count 
  FROM transactions 
  WHERE metadata->>'migrated_from' = 'financial_transactions';
  
  -- Only proceed if migration was successful
  IF ft_count != t_migrated_count THEN
    RAISE EXCEPTION 'Migration verification failed: financial_transactions has % records but only % were migrated to transactions', ft_count, t_migrated_count;
  END IF;
  
  RAISE NOTICE 'Migration verification passed: % records successfully migrated', ft_count;
END $$;

-- Update any remaining references to use the unified transactions table
-- Check for any views or functions that might reference financial_transactions
DO $$
DECLARE
  view_count INTEGER;
BEGIN
  -- Count views that reference financial_transactions
  SELECT COUNT(*) INTO view_count
  FROM information_schema.views 
  WHERE view_definition LIKE '%financial_transactions%'
    AND table_schema = 'public'
    AND table_name != 'financial_transactions_view'; -- Exclude our compatibility view
  
  IF view_count > 0 THEN
    RAISE WARNING 'Found % views that reference financial_transactions table. Manual review may be needed.', view_count;
  END IF;
END $$;

-- Add RLS policies for the new columns in transactions table
-- Enable RLS if not already enabled
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Create policies for the new columns (fee_amount, payment_method, event_id, net_amount)
-- Users can view their own transactions
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'transactions' 
    AND policyname = 'Users can view own transactions'
  ) THEN
    CREATE POLICY "Users can view own transactions" ON transactions
      FOR SELECT USING (auth.uid() = user_id);
  END IF;
  
  -- Admins can view all transactions
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'transactions' 
    AND policyname = 'Admins can view all transactions'
  ) THEN
    CREATE POLICY "Admins can view all transactions" ON transactions
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM user_roles ur 
          WHERE ur.user_id = auth.uid() 
          AND ur.role = 'admin'
        )
      );
  END IF;
  
  -- Event managers can view transactions for their events
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'transactions' 
    AND policyname = 'Event managers can view event transactions'
  ) THEN
    CREATE POLICY "Event managers can view event transactions" ON transactions
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM events e 
          WHERE e.id = transactions.event_id 
          AND e.event_manager_id = auth.uid()
        )
      );
  END IF;
  
  -- System can insert transactions
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'transactions' 
    AND policyname = 'System can insert transactions'
  ) THEN
    CREATE POLICY "System can insert transactions" ON transactions
      FOR INSERT WITH CHECK (true);
  END IF;
  
  -- System can update transactions
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'transactions' 
    AND policyname = 'System can update transactions'
  ) THEN
    CREATE POLICY "System can update transactions" ON transactions
      FOR UPDATE USING (true);
  END IF;
END $$;

-- Create a backup of financial_transactions before dropping (just in case)
CREATE TABLE IF NOT EXISTS financial_transactions_backup AS 
SELECT * FROM financial_transactions;

-- Add a comment to the backup table
COMMENT ON TABLE financial_transactions_backup IS 'Backup of financial_transactions table before migration to unified transactions table';

-- Drop the old financial_transactions table
-- Note: This will also drop any dependent views, triggers, etc.
DROP TABLE IF EXISTS financial_transactions CASCADE;

-- Update the documentation comment for transactions table
COMMENT ON TABLE transactions IS 'Unified financial transactions table for all payment, refund, withdrawal, and fee operations. Replaces the old financial_transactions table.';

-- Log the cleanup
INSERT INTO activity_logs (
  user_id,
  action,
  entity_type,
  entity_id,
  details,
  category
) VALUES (
  NULL,
  'cleanup_financial_transactions',
  'system',
  gen_random_uuid(),
  jsonb_build_object(
    'migration', 'cleanup_financial_transactions_table',
    'timestamp', NOW(),
    'description', 'Dropped redundant financial_transactions table after successful merge to transactions table',
    'backup_table', 'financial_transactions_backup'
  ),
  'system_maintenance'
);

-- Verify the cleanup was successful
DO $$
BEGIN
  -- Check that financial_transactions table no longer exists
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'financial_transactions'
  ) THEN
    RAISE EXCEPTION 'Cleanup failed: financial_transactions table still exists';
  END IF;
  
  -- Check that backup table exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'financial_transactions_backup'
  ) THEN
    RAISE WARNING 'Backup table financial_transactions_backup was not created';
  END IF;
  
  RAISE NOTICE 'Cleanup completed successfully. Old financial_transactions table removed, backup created.';
END $$;
