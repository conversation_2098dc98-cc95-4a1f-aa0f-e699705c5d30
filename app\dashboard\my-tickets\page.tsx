"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { Eye, Receipt, Ticket, Download, FileText, Award, Users, X, CreditCard, LayoutGrid, Table, ArrowUpDown, ChevronUp, ChevronDown } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table as TableComponent, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { CertificateViewer } from "@/components/certificate-viewer"
import { DynamicQRCode } from "@/components/dynamic-qr-code"
import { getEventInitials, generateEventGradient, formatDateBadge, getEventDisplayImage } from "@/lib/utils"

type SortField = "event" | "attendee" | "date" | "status" | "payment"
type SortDirection = "asc" | "desc"

export default function MyTicketsPage() {
  const [tickets, setTickets] = useState<any[]>([])
  const [certificates, setCertificates] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedTicket, setSelectedTicket] = useState<any>(null)
  const [viewMode, setViewMode] = useState<"ticket" | "receipt" | "group-receipt">("ticket")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [groupReceiptData, setGroupReceiptData] = useState<any>(null)
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({})
  const [groupFilter, setGroupFilter] = useState<"all" | "personal" | "group">("all")
  const [paymentFilter, setPaymentFilter] = useState<"all" | "pending" | "paid" | "processing">("all")
  const [displayMode, setDisplayMode] = useState<"card" | "table">("card")
  const [sortField, setSortField] = useState<SortField>("date")
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc")
  const { user } = useAuth()
  const { toast } = useToast()

  // Set page title
  useEffect(() => {
    document.title = "My Tickets | mTicket.my - Event Management Platform"
  }, [])

  // Handle payment redirect parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const paymentStatus = urlParams.get('payment');
    const registrationId = urlParams.get('registration');

    if (paymentStatus && registrationId) {
      if (paymentStatus === 'success') {
        toast({
          title: "Payment Successful",
          description: "Your payment has been processed successfully. Your registration is now confirmed.",
        });

        // Verify payment status
        const verifyPayment = async () => {
          try {
            const token = document.cookie
              .split('; ')
              .find(row => row.startsWith('auth_token='))
              ?.split('=')[1];

            if (token) {
              await fetch('/api/registrations/verify-payment', {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  registration_id: registrationId,
                  status: 'paid'
                }),
              });
            }
          } catch (error) {
            console.error("Error verifying payment:", error);
          }
        };

        verifyPayment();
      } else if (paymentStatus === 'cancelled') {
        toast({
          title: "Payment Cancelled",
          description: "Your payment was cancelled. You can try again anytime.",
          variant: "destructive",
        });
      }

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [toast])

  useEffect(() => {
    const fetchTickets = async () => {
      setLoading(true)
      try {
        // Get auth token from cookie first
        const token = document.cookie
          .split('; ')
          .find(row => row.startsWith('auth_token='))
          ?.split('=')[1];

        if (!token) {
          console.log("No authentication token found, user may not be logged in yet")
          setTickets([])
          setLoading(false)
          return
        }

        // Fetch tickets from API
        const response = await fetch('/api/dashboard/tickets', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          if (response.status === 401) {
            console.log("Authentication failed, user may need to log in again")
            setTickets([])
            setLoading(false)
            return
          }
          throw new Error(`Failed to fetch tickets: ${response.status}`)
        }

        const data = await response.json()
        console.log("Fetched tickets:", data)
        console.log("Current user ID:", user?.id)

        // Debug: Log each ticket's user_id and created_by
        if (data.tickets && data.tickets.length > 0) {
          console.log("=== TICKET ANALYSIS ===")
          data.tickets.forEach((ticket: any, index: number) => {
            const isOwnTicket = ticket.user_id === user?.id
            const isGroupTicket = ticket.created_by === user?.id && ticket.user_id !== user?.id
            console.log(`Ticket ${index + 1}:`, {
              id: ticket.id,
              attendee_name: ticket.guest_name || ticket.attendee_name,
              user_id: ticket.user_id,
              created_by: ticket.created_by,
              isOwnTicket,
              isGroupTicket,
              event_title: ticket.event?.title
            })
          })
          console.log("=== END ANALYSIS ===")
        }

        setTickets(data.tickets || [])

        // Fetch certificates for the user
        try {
          const certResponse = await fetch('/api/dashboard/certificates', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          })

          if (certResponse.ok) {
            const certData = await certResponse.json()
            setCertificates(certData.certificates || [])

            // Debug logging
            console.log("Fetched certificates:", certData?.certificates?.length || 0)
            console.log("Sample certificate:", certData?.certificates?.[0])
          }
        } catch (certError) {
          console.error("Error fetching certificates:", certError)
          // Don't show error for certificates as it's not critical
        }

        // Debug logging for tickets
        console.log("Fetched tickets:", data.tickets?.length || 0)
        console.log("Sample ticket:", data.tickets?.[0])

        // Debug: Check for group_registration_id in tickets
        if (data.tickets && data.tickets.length > 0) {
          console.log("=== GROUP REGISTRATION DEBUG ===")
          data.tickets.forEach((ticket: any, index: number) => {
            if (ticket.group_registration_id) {
              console.log(`Ticket ${index + 1} has group_registration_id:`, {
                id: ticket.id,
                attendee_name: ticket.guest_name || ticket.attendee_name,
                group_registration_id: ticket.group_registration_id,
                payment_status: ticket.payment_status,
                user_id: ticket.user_id,
                created_by: ticket.created_by
              })
            }
          })
          console.log("=== END GROUP DEBUG ===")
        }
      } catch (error) {
        console.error("Error fetching tickets:", error)

        // Only show error toast for actual errors, not auth issues
        if (error instanceof Error && error.message.includes("Failed to fetch tickets")) {
          toast({
            title: "Error",
            description: "Failed to load your tickets. Please try again.",
            variant: "destructive",
          })
        }

        setTickets([])
      } finally {
        setLoading(false)
      }
    }

    // Only fetch tickets if we're not in the initial loading state
    // This prevents the error when the auth context is still loading
    if (user !== null) {
      fetchTickets()
    } else if (user === null) {
      // If user is explicitly null (not logged in), stop loading
      setLoading(false)
    }
  }, [user, toast])

  const handleViewTicket = (ticket: any, mode: "ticket" | "receipt") => {
    setSelectedTicket(ticket)
    setViewMode(mode)
    setIsDialogOpen(true)
  }

  const handleViewGroupReceipt = async (ticket: any) => {
    console.log("handleViewGroupReceipt called with ticket:", {
      id: ticket.id,
      group_registration_id: ticket.group_registration_id,
      payment_status: ticket.payment_status,
      user_id: ticket.user_id,
      created_by: ticket.created_by,
      current_user: user?.id
    });

    if (!ticket.group_registration_id) {
      console.log("No group_registration_id found in ticket");
      toast({
        title: "Error",
        description: "This ticket is not part of a group registration.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Get auth token
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('auth_token='))
        ?.split('=')[1];

      console.log("Auth token found:", !!token);

      if (!token) {
        toast({
          title: "Authentication required",
          description: "Please log in to view group receipt.",
          variant: "destructive",
        });
        return;
      }

      // Fetch group registration data
      const url = `/api/dashboard/group-receipt?groupId=${ticket.group_registration_id}`;
      console.log("Fetching group receipt from:", url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log("Response status:", response.status);
      console.log("Response ok:", response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error Response:", errorText);
        throw new Error(`Failed to fetch group receipt data: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log("Group receipt data received:", data);

      setGroupReceiptData(data);
      setSelectedTicket(ticket);
      setViewMode("group-receipt");
      setIsDialogOpen(true);

    } catch (error) {
      console.error("Error fetching group receipt:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load group receipt. Please try again.",
        variant: "destructive",
      });
    }
  }

  const handleDownloadTicket = async () => {
    if (!selectedTicket) return;

    try {
      // Get auth token
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('auth_token='))
        ?.split('=')[1];

      if (!token) {
        toast({
          title: "Authentication required",
          description: "Please log in to download tickets.",
          variant: "destructive",
        });
        return;
      }

      // Open ticket in new tab for printing as PDF
      const ticketUrl = `/api/tickets/view-pdf?ticketId=${selectedTicket.id}&type=ticket`;
      window.open(ticketUrl, '_blank');

      toast({
        title: "Ticket opened",
        description: "Your ticket has been opened in a new tab. Use Ctrl+P to print as PDF.",
      });
    } catch (error) {
      console.error("Error opening ticket:", error);
      toast({
        title: "Failed to open ticket",
        description: "Failed to open ticket. Please try again.",
        variant: "destructive",
      });
    }
  }

  const handleDownloadReceipt = async () => {
    if (!selectedTicket) return;

    try {
      // Get auth token
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('auth_token='))
        ?.split('=')[1];

      if (!token) {
        toast({
          title: "Authentication required",
          description: "Please log in to download receipts.",
          variant: "destructive",
        });
        return;
      }

      // Open receipt in new tab for printing as PDF
      const receiptUrl = `/api/tickets/view-pdf?ticketId=${selectedTicket.id}&type=receipt`;
      window.open(receiptUrl, '_blank');

      toast({
        title: "Receipt opened",
        description: "Your receipt has been opened in a new tab. Use Ctrl+P to print as PDF.",
      });
    } catch (error) {
      console.error("Error opening receipt:", error);
      toast({
        title: "Failed to open receipt",
        description: "Failed to open receipt. Please try again.",
        variant: "destructive",
      });
    }
  }

  const handleDownloadGroupReceipt = async () => {
    if (!selectedTicket || !selectedTicket.group_registration_id) return;

    try {
      // Get auth token
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('auth_token='))
        ?.split('=')[1];

      if (!token) {
        toast({
          title: "Authentication required",
          description: "Please log in to download group receipt.",
          variant: "destructive",
        });
        return;
      }

      // Open group receipt in new tab for printing as PDF
      const groupReceiptUrl = `/api/tickets/view-pdf?groupId=${selectedTicket.group_registration_id}&type=group-receipt`;
      window.open(groupReceiptUrl, '_blank');

      toast({
        title: "Group receipt opened",
        description: "Your group receipt has been opened in a new tab. Use Ctrl+P to print as PDF.",
      });
    } catch (error) {
      console.error("Error opening group receipt:", error);
      toast({
        title: "Failed to open group receipt",
        description: "Failed to open group receipt. Please try again.",
        variant: "destructive",
      });
    }
  }

  const handlePayNow = async (ticket: any) => {
    try {
      // Get auth token
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('auth_token='))
        ?.split('=')[1];

      if (!token) {
        toast({
          title: "Authentication required",
          description: "Please log in to make payment.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Processing payment",
        description: "Redirecting to payment gateway...",
      });

      // Call payment API
      const response = await fetch('/api/registrations/payment', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          registration_id: ticket.id,
          amount: ticket.payment_amount || ticket.event?.price || 0,
          currency: 'MYR',
          description: `Payment for ${ticket.event?.title || 'Event'}`,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.payment_url) {
          // Redirect to payment gateway
          window.location.href = result.payment_url;
        } else {
          throw new Error(result.error || 'Failed to initiate payment');
        }
      } else {
        throw new Error('Failed to process payment request');
      }
    } catch (error) {
      console.error("Error processing payment:", error);
      toast({
        title: "Payment failed",
        description: "Failed to process payment. Please try again.",
        variant: "destructive",
      });
    }
  }

  const handleViewCertificate = async (ticket: any) => {
    try {
      // Check if certificate already exists
      const existingCertificate = certificates.find(cert => cert.registration_id === ticket.id);

      if (existingCertificate) {
        // Open certificate in new window
        window.open(`/certificates/verify/${existingCertificate.unique_code}`, "_blank");

        toast({
          title: "Certificate opened",
          description: "Your certificate has been opened in a new window",
        });
      } else {
        // Check if event is past and user attended
        const eventEndDate = new Date(ticket.event?.end_date || "");
        const now = new Date();

        if (eventEndDate < now && ticket.status === "attended") {
          // Generate certificate
          toast({
            title: "Generating certificate",
            description: "Your certificate is being generated. Please wait...",
          });

          // Call certificate generation API
          try {
            const token = document.cookie
              .split('; ')
              .find(row => row.startsWith('auth_token='))
              ?.split('=')[1];

            const response = await fetch('/api/certificates/generate', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                eventId: ticket.event?.id,
                participantId: ticket.id,
                templateId: 'default-template',
                participantName: ticket.guest_name,
              }),
            });

            if (response.ok) {
              const result = await response.json();
              window.open(`/certificates/verify/${result.certificate.verification_code}`, "_blank");
              toast({
                title: "Certificate ready",
                description: "Your certificate has been generated and opened",
              });

              // Refresh certificates list
              window.location.reload();
            } else {
              throw new Error('Failed to generate certificate');
            }
          } catch (genError) {
            console.error("Error generating certificate:", genError);
            toast({
              title: "Generation failed",
              description: "Failed to generate certificate. Please try again later.",
              variant: "destructive",
            });
          }
        } else if (eventEndDate >= now) {
          toast({
            title: "Certificate not available",
            description: "Certificates are only available after the event has ended",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Certificate not available",
            description: "You need to attend the event to receive a certificate",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error handling certificate:", error);
      toast({
        title: "Error",
        description: "Failed to process certificate request",
        variant: "destructive",
      });
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return <Badge className="bg-green-100 text-green-800">Confirmed</Badge>
      case "attended":
        return <Badge className="bg-blue-100 text-blue-800">Attended</Badge>
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPaymentStatusBadge = (status: string, isClickable: boolean = true) => {
    const handlePaymentFilterClick = (filterStatus: string) => {
      if (isClickable) {
        setPaymentFilter(filterStatus as "all" | "pending" | "paid" | "processing")
        toast({
          title: "Filter applied",
          description: `Showing ${filterStatus === "all" ? "all payments" : filterStatus + " payments only"}`,
        })
      }
    }

    const baseClasses = isClickable ? "cursor-pointer hover:opacity-80 transition-opacity" : ""

    switch (status) {
      case "paid":
        return (
          <Badge
            className={`bg-green-100 text-green-800 ${baseClasses}`}
            onClick={() => handlePaymentFilterClick("paid")}
          >
            Paid
          </Badge>
        )
      case "pending":
        return (
          <Badge
            className={`bg-orange-100 text-orange-800 ${baseClasses}`}
            onClick={() => handlePaymentFilterClick("pending")}
          >
            Pending Payment
          </Badge>
        )
      case "processing":
        return (
          <Badge
            className={`bg-blue-100 text-blue-800 ${baseClasses}`}
            onClick={() => handlePaymentFilterClick("processing")}
          >
            Processing
          </Badge>
        )
      case "free":
        return (
          <Badge
            className={`bg-gray-100 text-gray-800 ${baseClasses}`}
            onClick={() => handlePaymentFilterClick("paid")}
          >
            Free
          </Badge>
        )
      case "refunded":
        return (
          <Badge
            className={`bg-red-100 text-red-800 ${baseClasses}`}
            onClick={() => handlePaymentFilterClick("paid")}
          >
            Refunded
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const isPastEvent = (endDate: string) => {
    return new Date(endDate) < new Date()
  }

  const handleImageError = (ticketId: string) => {
    setImageErrors(prev => ({ ...prev, [ticketId]: true }))
  }

  // Helper function to check if this is a group registration created by the user
  const isGroupRegistration = (ticket: any) => {
    const result = ticket.created_by === user?.id && ticket.user_id !== user?.id
    console.log("Group registration check:", {
      ticket_id: ticket.id,
      attendee_name: ticket.guest_name || ticket.attendee_name,
      user_id: ticket.user_id,
      created_by: ticket.created_by,
      current_user: user?.id,
      isGroup: result
    })
    return result
  }

  // Filter tickets based on group and payment filters
  const filterTickets = (ticketList: any[]) => {
    let filteredTickets = ticketList

    // Apply group filter
    if (groupFilter === "group") {
      filteredTickets = filteredTickets.filter(ticket => isGroupRegistration(ticket))
    } else if (groupFilter === "personal") {
      filteredTickets = filteredTickets.filter(ticket => !isGroupRegistration(ticket))
    }

    // Apply payment filter
    if (paymentFilter !== "all") {
      filteredTickets = filteredTickets.filter(ticket => {
        const paymentStatus = ticket.payment_status || 'pending'
        return paymentStatus === paymentFilter
      })
    }

    return filteredTickets
  }

  // Handle group filter click
  const handleGroupFilterClick = (filterType: "all" | "personal" | "group") => {
    setGroupFilter(filterType)
    toast({
      title: "Filter applied",
      description: `Showing ${filterType === "all" ? "all tickets" : filterType === "group" ? "group registrations only" : "personal tickets only"}`,
    })
  }

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // Sort tickets
  const sortTickets = (ticketList: any[]) => {
    return [...ticketList].sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortField) {
        case "event":
          aValue = a.event?.title || ""
          bValue = b.event?.title || ""
          break
        case "attendee":
          aValue = a.guest_name || ""
          bValue = b.guest_name || ""
          break
        case "date":
          aValue = new Date(a.event?.start_date || "")
          bValue = new Date(b.event?.start_date || "")
          break
        case "status":
          aValue = a.status || ""
          bValue = b.status || ""
          break
        case "payment":
          aValue = a.payment_status || "pending"
          bValue = b.payment_status || "pending"
          break
        default:
          return 0
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
      return 0
    })
  }

  // Get sort icon
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4" />
    }
    return sortDirection === "asc" ?
      <ChevronUp className="h-4 w-4" /> :
      <ChevronDown className="h-4 w-4" />
  }



  // Render table view for tickets
  const renderTableView = (ticketList: any[]) => {
    if (ticketList.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <Ticket className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">
            {groupFilter === "all"
              ? "No tickets found"
              : groupFilter === "group"
              ? "No group registrations found"
              : "No personal tickets found"}
          </h3>
          <p className="text-muted-foreground mt-1">
            {groupFilter === "all"
              ? "You don't have any tickets"
              : groupFilter === "group"
              ? "You haven't created any group registrations"
              : "You don't have any personal tickets"}
          </p>
        </div>
      )
    }

    // Sort the tickets before rendering
    const sortedTickets = sortTickets(ticketList)

    return (
      <TooltipProvider>
        <TableComponent>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                  onClick={() => handleSort("event")}
                >
                  Event
                  {getSortIcon("event")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                  onClick={() => handleSort("attendee")}
                >
                  Attendee
                  {getSortIcon("attendee")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                  onClick={() => handleSort("date")}
                >
                  Date
                  {getSortIcon("date")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                  onClick={() => handleSort("status")}
                >
                  Status
                  {getSortIcon("status")}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                  onClick={() => handleSort("payment")}
                >
                  Payment
                  {getSortIcon("payment")}
                </Button>
              </TableHead>
              <TableHead>Type</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedTickets.map((ticket) => (
              <TableRow key={ticket.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-100 flex items-center justify-center flex-shrink-0">
                      {ticket.event?.image_url && !imageErrors[ticket.id] ? (
                        <img
                          src={ticket.event.image_url}
                          alt={ticket.event?.title || "Event"}
                          className="h-full w-full object-cover"
                          onError={() => handleImageError(ticket.id)}
                        />
                      ) : (
                        (() => {
                          const gradient = generateEventGradient(ticket.event?.title || "Event")
                          return (
                            <div
                              className={`w-full h-full ${gradient.className} flex items-center justify-center`}
                              style={gradient.style}
                            >
                              <span className="text-white font-bold text-xs">
                                {getEventInitials(ticket.event?.title || "Event")}
                              </span>
                            </div>
                          )
                        })()
                      )}
                    </div>
                    <div>
                      <div className="font-medium line-clamp-1">{ticket.event?.title || "Unknown Event"}</div>
                      <div className="text-sm text-muted-foreground">{ticket.event?.location || "TBA"}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{ticket.guest_name}</div>
                    <div className="text-sm text-muted-foreground">{ticket.guest_email}</div>
                    {ticket.ic_reg && (
                      <div className="text-xs text-muted-foreground">IC/Reg: {ticket.ic_reg}</div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {new Date(ticket.event?.start_date || "").toLocaleDateString()}
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(ticket.status)}
                </TableCell>
                <TableCell>
                  {getPaymentStatusBadge(ticket.payment_status || 'pending')}
                </TableCell>
                <TableCell>
                  {isGroupRegistration(ticket) && (
                    <Badge
                      variant="outline"
                      className="bg-blue-50 text-blue-700 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                      onClick={() => handleGroupFilterClick("group")}
                    >
                      <Users className="mr-1 h-3 w-3" />
                      Group
                    </Badge>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-1 justify-end">
                    {(() => {
                      // Check payment status from the ticket data
                      const paymentStatus = ticket.payment_status || 'pending';
                      const isPending = paymentStatus === 'pending' && ticket.status === 'pending';

                      if (isPending) {
                        return (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                onClick={() => handlePayNow(ticket)}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <CreditCard className="mr-1 h-3 w-3" />
                                Pay Now
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Complete payment for this registration</p>
                            </TooltipContent>
                          </Tooltip>
                        );
                      } else {
                        return (
                          <>
                            {/* Receipt button with popover for individual and group receipts */}
                            {(() => {
                              const hasGroupId = !!ticket.group_registration_id;
                              const isPaid = ticket.payment_status === 'paid';
                              const hasIndividualReceipt = isPaid && (ticket.transaction?.receipt_number || ticket.payment_date);
                              const hasGroupReceipt = hasGroupId && isPaid;
                              const showReceiptButton = hasIndividualReceipt || hasGroupReceipt;

                              return showReceiptButton && (
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                    >
                                      <Receipt className="h-3 w-3" />
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-48" align="center" sideOffset={8}>
                                    {/* Arrow indicator */}
                                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                      <div className="w-4 h-4 bg-popover border-l border-t border-border rotate-45 transform origin-center"></div>
                                    </div>
                                    <div className="space-y-2">
                                      <h4 className="font-medium text-sm">Receipt Options</h4>
                                      {hasIndividualReceipt && (
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleViewTicket(ticket, "receipt")}
                                          className="w-full justify-start"
                                        >
                                          <Receipt className="mr-2 h-3 w-3" />
                                          Individual Receipt
                                        </Button>
                                      )}
                                      {hasGroupReceipt && (
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleViewGroupReceipt(ticket)}
                                          className="w-full justify-start"
                                        >
                                          <Users className="mr-2 h-3 w-3" />
                                          Group Receipt
                                        </Button>
                                      )}
                                    </div>
                                  </PopoverContent>
                                </Popover>
                              );
                            })()}
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  onClick={() => handleViewTicket(ticket, "ticket")}
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>View event ticket</p>
                              </TooltipContent>
                            </Tooltip>
                          </>
                        );
                      }
                    })()}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </TableComponent>
      </TooltipProvider>
    )
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8">
        <div>
          <h2 className="text-2xl font-bold">My Tickets</h2>
          <p className="text-muted-foreground">View and manage your event tickets and group registrations</p>
        </div>
        <div className="flex h-40 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      </div>
    )
  }

  // If user is not authenticated, show login message
  if (user === null) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8">
        <div>
          <h2 className="text-2xl font-bold">My Tickets</h2>
          <p className="text-muted-foreground">View and manage your event tickets and group registrations</p>
        </div>
        <div className="flex flex-col items-center justify-center py-12">
          <Ticket className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">Please log in to view your tickets</h3>
          <p className="text-muted-foreground mt-1">You need to be logged in to access your tickets</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8">
      <div>
        <h2 className="text-2xl font-bold">My Tickets</h2>
        <p className="text-muted-foreground">View and manage your event tickets and group registrations</p>
      </div>

      {/* Filter Controls */}
      <div className="space-y-3">
        {/* View Mode and Group Filter Row */}
        <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
          {/* View Mode Selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-muted-foreground">View:</span>
            <Select value={displayMode} onValueChange={(value: "card" | "table") => setDisplayMode(value)}>
              <SelectTrigger className="w-32 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="card">
                  <div className="flex items-center gap-2">
                    <LayoutGrid className="h-4 w-4" />
                    <span>Card</span>
                  </div>
                </SelectItem>
                <SelectItem value="table">
                  <div className="flex items-center gap-2">
                    <Table className="h-4 w-4" />
                    <span>Table</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Group Filter */}
          <div className="flex flex-wrap gap-2 items-center">
            <span className="text-sm font-medium text-muted-foreground">Filter by type:</span>
            <Button
              variant={groupFilter === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => handleGroupFilterClick("all")}
              className="h-8"
            >
              All Tickets
            </Button>
            <Button
              variant={groupFilter === "personal" ? "default" : "outline"}
              size="sm"
              onClick={() => handleGroupFilterClick("personal")}
              className="h-8"
            >
              Personal
            </Button>
            <Button
              variant={groupFilter === "group" ? "default" : "outline"}
              size="sm"
              onClick={() => handleGroupFilterClick("group")}
              className="h-8"
            >
              <Users className="mr-1 h-3 w-3" />
              Group
            </Button>
            {groupFilter !== "all" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleGroupFilterClick("all")}
                className="h-8 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Payment Filter */}
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm font-medium text-muted-foreground">Filter by payment:</span>
          <Button
            variant={paymentFilter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setPaymentFilter("all")}
            className="h-8"
          >
            All Payments
          </Button>
          <Button
            variant={paymentFilter === "pending" ? "default" : "outline"}
            size="sm"
            onClick={() => setPaymentFilter("pending")}
            className="h-8"
          >
            <div className="w-2 h-2 bg-orange-500 rounded-full mr-1"></div>
            Pending
          </Button>
          <Button
            variant={paymentFilter === "processing" ? "default" : "outline"}
            size="sm"
            onClick={() => setPaymentFilter("processing")}
            className="h-8"
          >
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
            Processing
          </Button>
          <Button
            variant={paymentFilter === "paid" ? "default" : "outline"}
            size="sm"
            onClick={() => setPaymentFilter("paid")}
            className="h-8"
          >
            <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
            Paid
          </Button>
          {paymentFilter !== "all" && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPaymentFilter("all")}
              className="h-8 text-muted-foreground hover:text-foreground"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="upcoming">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
          <TabsTrigger value="past">Past Events</TabsTrigger>
        </TabsList>
        <TabsContent value="upcoming" className="mt-6">
          {displayMode === "table" ? (
            renderTableView(filterTickets(tickets.filter((ticket) => !isPastEvent(ticket.event?.end_date || ""))))
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filterTickets(tickets.filter((ticket) => !isPastEvent(ticket.event?.end_date || ""))).length === 0 ? (
                <div className="md:col-span-2 lg:col-span-3 flex flex-col items-center justify-center py-12">
                  <Ticket className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">
                    {groupFilter === "all"
                      ? "No upcoming tickets"
                      : groupFilter === "group"
                      ? "No upcoming group registrations"
                      : "No upcoming personal tickets"}
                  </h3>
                  <p className="text-muted-foreground mt-1">
                    {groupFilter === "all"
                      ? "You don't have any tickets for upcoming events"
                      : groupFilter === "group"
                      ? "You haven't created any group registrations for upcoming events"
                      : "You don't have any personal tickets for upcoming events"}
                  </p>
                </div>
              ) : (
                filterTickets(tickets.filter((ticket) => !isPastEvent(ticket.event?.end_date || "")))
                  .map((ticket) => (
                  <Card key={ticket.id} className="flex flex-col h-full">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg line-clamp-2 min-h-[3.5rem] flex items-start">{ticket.event?.title || "Unknown Event"}</CardTitle>
                        <div className="flex flex-col gap-1">
                          {getStatusBadge(ticket.status)}
                          {isGroupRegistration(ticket) && (
                            <Badge
                              variant="outline"
                              className="bg-blue-50 text-blue-700 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                              onClick={() => handleGroupFilterClick("group")}
                            >
                              <Users className="mr-1 h-3 w-3" />
                              Group
                            </Badge>
                          )}
                        </div>
                      </div>
                      <CardDescription>{new Date(ticket.event?.start_date || "").toLocaleDateString()}</CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 flex flex-col">
                      <div className="aspect-video w-full rounded-md overflow-hidden mb-4 bg-gray-100 flex items-center justify-center relative">
                        {(() => {
                          const displayImage = getEventDisplayImage(ticket.event)
                          return displayImage && !imageErrors[ticket.id] ? (
                            <Image
                              src={displayImage.url}
                              alt={displayImage.alt}
                              fill
                              className="object-cover"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              onError={() => handleImageError(ticket.id)}
                            />
                          ) : (
                            (() => {
                              const gradient = generateEventGradient(ticket.event?.title || "Event")
                              return (
                                <div
                                  className={`w-full h-full ${gradient.className} flex items-center justify-center`}
                                  style={gradient.style}
                                >
                                  <span className="text-white font-bold text-lg md:text-xl">
                                    {getEventInitials(ticket.event?.title || "Event")}
                                  </span>
                                </div>
                              )
                            })()
                          )
                        })()}

                        {/* Date Badge - Top Right */}
                        {ticket.event?.start_date && (() => {
                          const dateBadge = formatDateBadge(ticket.event.start_date)
                          return (
                            <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm border border-white/20 rounded-md px-2 py-1 text-center shadow-sm min-w-[50px]">
                              <div className="text-lg font-bold text-gray-800 leading-none">
                                {dateBadge.day}
                              </div>
                              <div className="text-xs font-bold text-gray-700 uppercase leading-none">
                                {dateBadge.month}
                              </div>
                              <div className="text-xs text-gray-600 leading-none">
                                {dateBadge.dayOfWeek}
                              </div>
                            </div>
                          )
                        })()}
                      </div>
                      <div className="space-y-2 text-sm flex-1">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Registration Code:</span>
                          <span className="font-medium">{ticket.registration_code || "N/A"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Ticket #:</span>
                          <span className="font-medium">{ticket.id.substring(0, 8).toUpperCase()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Location:</span>
                          <span className="font-medium truncate ml-2">{ticket.event?.location || "TBA"}</span>
                        </div>
                        {ticket.ic_reg && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">IC/Reg:</span>
                            <span className="font-medium">{ticket.ic_reg}</span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-2 mt-auto">
                      {/* Show different buttons based on payment status */}
                      {(() => {
                        // Check payment status from the ticket data
                        const paymentStatus = ticket.payment_status || 'pending';
                        const isPending = paymentStatus === 'pending' && ticket.status === 'pending';

                        if (isPending) {
                          return (
                            <Button
                              size="sm"
                              onClick={() => handlePayNow(ticket)}
                              className="w-full bg-green-600 hover:bg-green-700"
                            >
                              <CreditCard className="mr-2 h-4 w-4" />
                              Pay Now - RM {(ticket.payment_amount || ticket.event?.price || 0).toFixed(2)}
                            </Button>
                          );
                        } else {
                          return (
                            <>
                              <div className="flex gap-2 w-full">
                                {/* Receipt button with popover for individual and group receipts */}
                                {(() => {
                                  const hasGroupId = !!ticket.group_registration_id;
                                  const isPaid = ticket.payment_status === 'paid';
                                  const hasIndividualReceipt = isPaid && (ticket.transaction?.receipt_number || ticket.payment_date);
                                  const hasGroupReceipt = hasGroupId && isPaid;
                                  const showReceiptButton = hasIndividualReceipt || hasGroupReceipt;

                                  return showReceiptButton && (
                                    <Popover>
                                      <PopoverTrigger asChild>
                                        <Button variant="outline" size="sm" className="flex-1">
                                          <Receipt className="mr-2 h-4 w-4" />
                                          Receipt
                                        </Button>
                                      </PopoverTrigger>
                                      <PopoverContent className="w-56" align="center" sideOffset={8}>
                                        {/* Arrow indicator */}
                                        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                          <div className="w-4 h-4 bg-popover border-l border-t border-border rotate-45 transform origin-center"></div>
                                        </div>
                                        <div className="space-y-2">
                                          <h4 className="font-medium text-sm">Receipt Options</h4>
                                          {hasIndividualReceipt && (
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => handleViewTicket(ticket, "receipt")}
                                              className="w-full justify-start"
                                            >
                                              <Receipt className="mr-2 h-4 w-4" />
                                              Individual Receipt
                                            </Button>
                                          )}
                                          {hasGroupReceipt && (
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => handleViewGroupReceipt(ticket)}
                                              className="w-full justify-start"
                                            >
                                              <Users className="mr-2 h-4 w-4" />
                                              Group Receipt
                                            </Button>
                                          )}
                                        </div>
                                      </PopoverContent>
                                    </Popover>
                                  );
                                })()}
                                <Button size="sm" onClick={() => handleViewTicket(ticket, "ticket")} className="flex-1">
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Ticket
                                </Button>
                              </div>
                            </>
                          );
                        }
                      })()}
                    </CardFooter>
                  </Card>
                ))
              )}
            </div>
          )}
        </TabsContent>
        <TabsContent value="past" className="mt-6">
          {displayMode === "table" ? (
            renderTableView(filterTickets(tickets.filter((ticket) => isPastEvent(ticket.event?.end_date || ""))))
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filterTickets(tickets.filter((ticket) => isPastEvent(ticket.event?.end_date || ""))).length === 0 ? (
                <div className="md:col-span-2 lg:col-span-3 flex flex-col items-center justify-center py-12">
                  <Ticket className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">
                    {groupFilter === "all"
                      ? "No past tickets"
                      : groupFilter === "group"
                      ? "No past group registrations"
                      : "No past personal tickets"}
                  </h3>
                  <p className="text-muted-foreground mt-1">
                    {groupFilter === "all"
                      ? "You don't have any tickets for past events"
                      : groupFilter === "group"
                      ? "You haven't created any group registrations for past events"
                      : "You don't have any personal tickets for past events"}
                  </p>
                </div>
              ) : (
                filterTickets(tickets.filter((ticket) => isPastEvent(ticket.event?.end_date || "")))
                  .map((ticket) => (
                  <Card key={ticket.id} className="flex flex-col h-full">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg line-clamp-2 min-h-[3.5rem] flex items-start">{ticket.event?.title || "Unknown Event"}</CardTitle>
                        <div className="flex flex-col gap-1">
                          {getStatusBadge(ticket.status)}
                          {isGroupRegistration(ticket) && (
                            <Badge
                              variant="outline"
                              className="bg-blue-50 text-blue-700 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                              onClick={() => handleGroupFilterClick("group")}
                            >
                              <Users className="mr-1 h-3 w-3" />
                              Group
                            </Badge>
                          )}
                        </div>
                      </div>
                      <CardDescription>{new Date(ticket.event?.start_date || "").toLocaleDateString()}</CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 flex flex-col">
                      <div className="aspect-video w-full rounded-md overflow-hidden mb-4 bg-gray-100 flex items-center justify-center relative">
                        {(() => {
                          const displayImage = getEventDisplayImage(ticket.event)
                          return displayImage && !imageErrors[ticket.id] ? (
                            <Image
                              src={displayImage.url}
                              alt={displayImage.alt}
                              fill
                              className="object-cover"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              onError={() => handleImageError(ticket.id)}
                            />
                          ) : (
                            (() => {
                              const gradient = generateEventGradient(ticket.event?.title || "Event")
                              return (
                                <div
                                  className={`w-full h-full ${gradient.className} flex items-center justify-center`}
                                  style={gradient.style}
                                >
                                  <span className="text-white font-bold text-lg md:text-xl">
                                    {getEventInitials(ticket.event?.title || "Event")}
                                  </span>
                                </div>
                              )
                            })()
                          )
                        })()}

                        {/* Date Badge - Top Right */}
                        {ticket.event?.start_date && (() => {
                          const dateBadge = formatDateBadge(ticket.event.start_date)
                          return (
                            <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm border border-white/20 rounded-md px-2 py-1 text-center shadow-sm min-w-[50px]">
                              <div className="text-lg font-bold text-gray-800 leading-none">
                                {dateBadge.day}
                              </div>
                              <div className="text-xs font-bold text-gray-700 uppercase leading-none">
                                {dateBadge.month}
                              </div>
                              <div className="text-xs text-gray-600 leading-none">
                                {dateBadge.dayOfWeek}
                              </div>
                            </div>
                          )
                        })()}
                      </div>
                      <div className="space-y-2 text-sm flex-1">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Registration Code:</span>
                          <span className="font-medium">{ticket.registration_code || "N/A"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Ticket #:</span>
                          <span className="font-medium">{ticket.id.substring(0, 8).toUpperCase()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Status:</span>
                          <span className="font-medium">{ticket.status}</span>
                        </div>
                        {ticket.ic_reg && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">IC/Reg:</span>
                            <span className="font-medium">{ticket.ic_reg}</span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter className="flex flex-col gap-2 mt-auto">
                      {/* Check if certificate exists for this ticket */}
                      {(() => {
                        const certificate = certificates.find(cert =>
                          cert.registration_id === ticket.id
                        )

                        return certificate ? (
                          <div className="w-full">
                            <div className="flex items-center gap-2 mb-2">
                              <Award className="h-4 w-4 text-green-600" />
                              <span className="text-sm font-medium text-green-600">Certificate Available</span>
                            </div>
                            <CertificateViewer
                              certificate={certificate}
                              eventTitle={ticket.event?.title || "Unknown Event"}
                              eventDate={ticket.event?.start_date || ""}
                              participantName={ticket.guest_name}
                            />
                          </div>
                        ) : (
                          <div className="w-full">
                            <div className="flex items-center gap-2 mb-2">
                              <FileText className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm text-muted-foreground">
                                {ticket.status === "attended" ? "Certificate pending" : "Certificate not available"}
                              </span>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewCertificate(ticket)}
                              className="w-full"
                              disabled={ticket.status !== "attended"}
                            >
                              <FileText className="mr-2 h-4 w-4" />
                              {ticket.status === "attended" ? "Request Certificate" : "Certificate N/A"}
                            </Button>
                          </div>
                        )
                      })()}

                      <div className="flex gap-2 w-full">
                        {/* Receipt button with popover for individual and group receipts */}
                        {(() => {
                          const hasGroupId = !!ticket.group_registration_id;
                          const isPaid = ticket.payment_status === 'paid';
                          const hasIndividualReceipt = isPaid && (ticket.transaction?.receipt_number || ticket.payment_date);
                          const hasGroupReceipt = hasGroupId && isPaid;
                          const showReceiptButton = hasIndividualReceipt || hasGroupReceipt;

                          return showReceiptButton && (
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="outline" size="sm" className="flex-1">
                                  <Receipt className="mr-2 h-4 w-4" />
                                  Receipt
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-56" align="center" sideOffset={8}>
                                {/* Arrow indicator */}
                                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                  <div className="w-4 h-4 bg-popover border-l border-t border-border rotate-45 transform origin-center"></div>
                                </div>
                                <div className="space-y-2">
                                  <h4 className="font-medium text-sm">Receipt Options</h4>
                                  {hasIndividualReceipt && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleViewTicket(ticket, "receipt")}
                                      className="w-full justify-start"
                                    >
                                      <Receipt className="mr-2 h-4 w-4" />
                                      Individual Receipt
                                    </Button>
                                  )}
                                  {hasGroupReceipt && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleViewGroupReceipt(ticket)}
                                      className="w-full justify-start"
                                    >
                                      <Users className="mr-2 h-4 w-4" />
                                      Group Receipt
                                    </Button>
                                  )}
                                </div>
                              </PopoverContent>
                            </Popover>
                          );
                        })()}
                        <Button size="sm" onClick={() => handleViewTicket(ticket, "ticket")} className="flex-1">
                          <Eye className="mr-2 h-4 w-4" />
                          View Ticket
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                ))
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Ticket/Receipt Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[85vh] overflow-y-auto w-[95vw] sm:w-full">
          <DialogHeader>
            <DialogTitle>
              {viewMode === "ticket" ? "Event Ticket" :
               viewMode === "group-receipt" ? "Group Payment Receipt" : "Payment Receipt"}
            </DialogTitle>
            <DialogDescription>
              {viewMode === "ticket" ? "Show this ticket at the event entrance" :
               viewMode === "group-receipt" ? "Complete receipt for group registration" : "Receipt for your payment"}
            </DialogDescription>
          </DialogHeader>

          {selectedTicket && (
            <>
              {viewMode === "ticket" ? (
                <div className="py-2">
                  {/* Event Header */}
                  <div className="text-center mb-4">
                    <h3 className="text-lg font-bold">{selectedTicket.event?.title || "Unknown Event"}</h3>
                    <p className="text-sm text-muted-foreground">
                      {new Date(selectedTicket.event?.start_date || "").toLocaleDateString()} at{" "}
                      {selectedTicket.event?.location || "TBA"}
                    </p>
                  </div>

                  {/* Two Column Layout - Ticket Style */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 min-h-[320px]">
                    {/* Left Column - QR Code */}
                    <div className="flex flex-col items-center justify-center space-y-3 bg-gradient-to-br from-blue-50 to-purple-50 p-4 rounded-lg border-2 border-dashed border-blue-200">
                      <DynamicQRCode
                        ticketData={{
                          id: selectedTicket.id,
                          event_id: selectedTicket.event?.id || "",
                          guest_name: selectedTicket.guest_name,
                          event: selectedTicket.event
                        }}
                        size={160}
                        showTimer={true}
                        showSecurityBadge={true}
                        isCheckedIn={selectedTicket.status === "attended"}
                      />

                      <div className="text-center space-y-1">
                        <p className="font-bold text-lg text-blue-900">{selectedTicket.id.substring(0, 8).toUpperCase()}</p>
                        <p className="text-blue-600 text-sm font-medium">Ticket ID</p>
                      </div>

                      <div className="text-center">
                        <p className="text-xs text-blue-700 font-medium">
                          {selectedTicket.event?.location || "Event Location"}
                        </p>
                        <p className="text-xs text-blue-600">
                          {new Date(selectedTicket.event?.start_date || "").toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {/* Right Column - Ticket Details */}
                    <div className="flex flex-col justify-center space-y-4 bg-gradient-to-br from-gray-50 to-blue-50 p-4 rounded-lg border-2 border-dashed border-gray-200">
                      {/* Attendee Section */}
                      <div className="text-center space-y-2">
                        <h4 className="font-bold text-lg text-gray-800 border-b-2 border-blue-200 pb-1">ADMIT ONE</h4>
                        <div className="space-y-1">
                          <p className="text-base font-semibold text-gray-900">{selectedTicket.guest_name}</p>
                          <p className="text-xs text-gray-600">{selectedTicket.guest_email}</p>
                          {selectedTicket.ic_reg && (
                            <p className="text-xs text-gray-600">IC/Reg: {selectedTicket.ic_reg}</p>
                          )}
                        </div>
                      </div>

                      {/* Status Section */}
                      <div className="text-center space-y-2">
                        <div className="flex justify-center">
                          {getStatusBadge(selectedTicket.status)}
                        </div>
                        <div className="space-y-1">
                          <p className={`text-xs font-medium ${selectedTicket.checked_in_at ? 'text-green-600' : 'text-orange-600'}`}>
                            {selectedTicket.checked_in_at ? "✓ CHECKED IN" : "⏳ PENDING CHECK-IN"}
                          </p>
                          {selectedTicket.checked_in_at && (
                            <p className="text-xs text-gray-500">
                              {new Date(selectedTicket.checked_in_at).toLocaleString()}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Registration Info */}
                      <div className="text-center space-y-1 border-t border-gray-200 pt-3">
                        <p className="text-xs text-gray-500 uppercase tracking-wide">Registration Code</p>
                        <p className="font-mono text-xs font-medium text-gray-700">
                          {selectedTicket.registration_code || "N/A"}
                        </p>
                      </div>

                      {/* Footer Note */}
                      <div className="text-center">
                        <p className="text-xs text-gray-500 italic">
                          Present QR code at entrance • Keep this ticket safe
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : viewMode === "receipt" ? (
                <div className="py-2">
                  {/* Receipt Header */}
                  <div className="text-center mb-4">
                    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3 rounded-lg mb-3">
                      <h3 className="text-xl font-bold">mTicket.my</h3>
                      <p className="text-sm opacity-90">Official Payment Receipt</p>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800">
                      {selectedTicket.event?.title || "Unknown Event"}
                    </h4>
                  </div>

                  {/* Receipt Content */}
                  <div className="max-w-2xl mx-auto space-y-4">
                    {/* Receipt Information */}
                    <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-base mb-2 text-blue-800">Receipt Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-blue-700">Receipt Number:</span>
                          <span className="text-sm font-mono font-medium text-blue-800">
                            {selectedTicket.transaction?.receipt_number || `RCP-${selectedTicket.id.substring(0, 8).toUpperCase()}`}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-blue-700">Invoice Number:</span>
                          <span className="text-sm font-mono font-medium text-blue-800">
                            {selectedTicket.transaction?.invoice_number || `INV-${selectedTicket.id.substring(0, 8).toUpperCase()}`}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-blue-700">Transaction ID:</span>
                          <span className="text-sm font-mono font-medium">
                            {selectedTicket.transaction?.gateway_transaction_id || "N/A"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-blue-700">Payment Date:</span>
                          <span className="text-sm font-medium">
                            {selectedTicket.payment_date ?
                              new Date(selectedTicket.payment_date).toLocaleDateString('en-MY', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              }) :
                              new Date(selectedTicket.created_at).toLocaleDateString('en-MY', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })
                            }
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Customer Information */}
                    <div className="bg-white border border-gray-200 p-3 rounded-lg">
                      <h4 className="font-semibold text-base mb-2 text-gray-800">Customer Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-gray-600">Name:</span>
                          <span className="text-sm font-medium">{selectedTicket.guest_name || selectedTicket.attendee_name}</span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-gray-600">Email:</span>
                          <span className="text-sm font-medium">{selectedTicket.guest_email || selectedTicket.attendee_email}</span>
                        </div>
                        {selectedTicket.ic_reg && (
                          <div className="flex justify-between items-center py-1">
                            <span className="text-sm text-gray-600">IC/Registration No:</span>
                            <span className="text-sm font-medium">{selectedTicket.ic_reg}</span>
                          </div>
                        )}
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-gray-600">Registration Type:</span>
                          <span className="text-sm font-medium">
                            {selectedTicket.group_registration_id ? 'Group Registration' : 'Individual Registration'}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-gray-600">Payment Status:</span>
                          <div>{getPaymentStatusBadge(selectedTicket.payment_status || 'pending', false)}</div>
                        </div>
                      </div>
                    </div>

                    {/* Payment Details */}
                    <div className="bg-white border border-gray-200 p-3 rounded-lg">
                      <h4 className="font-semibold text-base mb-2 text-gray-800">Payment Details</h4>
                      <div className="border border-gray-100 rounded-lg p-2 mb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="font-medium text-gray-800">{selectedTicket.event?.title || "Event Registration"}</div>
                            <div className="text-sm text-gray-600">Registration for {selectedTicket.guest_name || selectedTicket.attendee_name}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-gray-800">
                              RM {(selectedTicket.payment_amount || selectedTicket.event?.price || 0).toFixed(2)}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Show group total if this is a group registration */}
                      {selectedTicket.group_registration_id && selectedTicket.transaction?.amount &&
                       parseFloat(selectedTicket.transaction.amount) > parseFloat(selectedTicket.payment_amount || '0') && (
                        <div className="bg-blue-50 p-2 rounded border border-blue-200">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm font-medium text-blue-800">Group Registration Total:</span>
                            <span className="text-sm font-bold text-blue-800">
                              RM {parseFloat(selectedTicket.transaction.amount).toFixed(2)}
                            </span>
                          </div>
                          <div className="text-xs text-blue-600">
                            This receipt shows your individual amount.
                            <button
                              onClick={() => handleViewGroupReceipt(selectedTicket)}
                              className="ml-1 underline hover:text-blue-800 font-medium"
                            >
                              View group receipt
                            </button>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Total */}
                    <div className="bg-gradient-to-r from-green-50 to-blue-50 p-3 rounded-lg border border-green-200">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-lg font-bold text-gray-800">Total Paid</span>
                        <span className="text-2xl font-bold text-green-600">
                          RM {(selectedTicket.payment_amount || selectedTicket.event?.price || 0).toFixed(2)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Currency: Malaysian Ringgit (MYR)
                      </div>
                    </div>

                    {/* Receipt Footer */}
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-3 rounded-lg border">
                      <h5 className="font-medium text-gray-800 mb-1 text-sm">Important Notes</h5>
                      <ul className="text-xs text-gray-600 space-y-1">
                        <li>• This is an official payment receipt from mTicket.my</li>
                        <li>• Keep this receipt for your records and tax purposes</li>
                        <li>• For support, contact us with receipt number: {selectedTicket.transaction?.receipt_number || `RCP-${selectedTicket.id.substring(0, 8).toUpperCase()}`}</li>
                        <li>• This receipt confirms your registration for the event</li>
                      </ul>
                      <div className="mt-2 pt-1 border-t border-gray-200">
                        <p className="text-xs text-gray-500 text-center">
                          Generated on {new Date().toLocaleDateString('en-MY', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : viewMode === "group-receipt" && groupReceiptData ? (
                <div className="py-2">
                  {/* Group Receipt Header */}
                  <div className="text-center mb-4">
                    <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-3 rounded-lg mb-3">
                      <h3 className="text-xl font-bold">mTicket.my</h3>
                      <p className="text-sm opacity-90">Group Payment Receipt</p>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800">
                      {groupReceiptData.group?.event?.title || "Unknown Event"}
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">
                      Group Registration - {groupReceiptData.group?.participantCount || 0} Participants
                    </p>
                  </div>

                  {/* Group Receipt Content */}
                  <div className="max-w-2xl mx-auto space-y-4">
                    {/* Group Receipt Information */}
                    <div className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                      <h4 className="font-semibold text-base mb-2 text-purple-800">Group Receipt Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-purple-700">Main Receipt Number:</span>
                          <span className="text-sm font-mono font-medium text-purple-800">
                            {groupReceiptData.group?.mainTransaction?.receipt_number || "N/A"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-purple-700">Main Invoice Number:</span>
                          <span className="text-sm font-mono font-medium text-purple-800">
                            {groupReceiptData.group?.mainTransaction?.invoice_number || "N/A"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-purple-700">Transaction ID:</span>
                          <span className="text-sm font-mono font-medium">
                            {groupReceiptData.group?.mainTransaction?.gateway_transaction_id || "N/A"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-1">
                          <span className="text-sm text-purple-700">Payment Date:</span>
                          <span className="text-sm font-medium">
                            {groupReceiptData.group?.paymentDate ?
                              new Date(groupReceiptData.group.paymentDate).toLocaleDateString('en-MY', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              }) :
                              "N/A"
                            }
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Group Summary */}
                    <div className="bg-white border border-gray-200 p-3 rounded-lg">
                      <h4 className="font-semibold text-base mb-2 text-gray-800">Group Registration Summary</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-2">
                        <div className="text-center p-2 bg-blue-50 rounded-lg">
                          <div className="text-xl font-bold text-blue-600">{groupReceiptData.group?.participantCount || 0}</div>
                          <div className="text-sm text-blue-700">Participants</div>
                        </div>
                        <div className="text-center p-2 bg-green-50 rounded-lg">
                          <div className="text-xl font-bold text-green-600">
                            RM {(groupReceiptData.group?.totalAmount || 0).toFixed(2)}
                          </div>
                          <div className="text-sm text-green-700">Total Amount</div>
                        </div>
                      </div>
                    </div>

                    {/* Participants List */}
                    <div className="bg-white border border-gray-200 p-3 rounded-lg">
                      <h4 className="font-semibold text-base mb-2 text-gray-800">Participants & Individual Receipts</h4>
                      <div className="space-y-2">
                        {groupReceiptData.participants?.map((participant: any, index: number) => (
                          <div key={participant.id} className="border border-gray-100 rounded-lg p-2 hover:bg-gray-50">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="font-medium text-gray-800">{participant.attendee_name}</div>
                                <div className="text-sm text-gray-600">{participant.attendee_email}</div>
                                {participant.ic_reg && (
                                  <div className="text-xs text-gray-500">IC/Reg: {participant.ic_reg}</div>
                                )}
                                <div className="text-xs text-blue-600 font-mono mt-1">
                                  Receipt: {participant.transaction?.receipt_number || "N/A"}
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-semibold text-gray-800">
                                  RM {parseFloat(participant.payment_amount || '0').toFixed(2)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  Status: {participant.payment_status || 'pending'}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Group Total */}
                    <div className="bg-gradient-to-r from-green-50 to-blue-50 p-3 rounded-lg border border-green-200">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-lg font-bold text-gray-800">Grand Total</span>
                        <span className="text-2xl font-bold text-green-600">
                          RM {(groupReceiptData.group?.totalAmount || 0).toFixed(2)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Currency: Malaysian Ringgit (MYR)
                      </div>
                    </div>

                    {/* Group Receipt Footer */}
                    <div className="bg-gradient-to-r from-gray-50 to-purple-50 p-3 rounded-lg border">
                      <h5 className="font-medium text-gray-800 mb-1 text-sm">Group Receipt Notes</h5>
                      <ul className="text-xs text-gray-600 space-y-1">
                        <li>• This is an official group payment receipt from mTicket.my</li>
                        <li>• This receipt covers all {groupReceiptData.group?.participantCount || 0} participants in this group registration</li>
                        <li>• Individual receipts are available for each participant</li>
                        <li>• Keep this receipt for your records and tax purposes</li>
                        <li>• For support, contact us with main receipt number: {groupReceiptData.group?.mainTransaction?.receipt_number || "N/A"}</li>
                      </ul>
                      <div className="mt-2 pt-1 border-t border-gray-200">
                        <p className="text-xs text-gray-500 text-center">
                          Generated on {new Date().toLocaleDateString('en-MY', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
            </>
          )}

          <DialogFooter className="mt-3 pt-2 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row gap-2 w-full">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)} className="flex-1 h-9">
                Close
              </Button>
              <Button
                onClick={
                  viewMode === "ticket" ? handleDownloadTicket :
                  viewMode === "group-receipt" ? handleDownloadGroupReceipt :
                  handleDownloadReceipt
                }
                className="flex-1 h-9"
              >
                <Download className="mr-2 h-4 w-4" />
                Download {
                  viewMode === "ticket" ? "Ticket" :
                  viewMode === "group-receipt" ? "Group Receipt" :
                  "Receipt"
                }
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
