-- Create event_custom_fields table for reusable custom field templates
-- This allows users to create and reuse custom field configurations across events

CREATE TABLE IF NOT EXISTS event_custom_fields (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_by UUID NULL REFERENCES users(id) ON DELETE SET NULL,
  name TEXT NOT NULL,
  description TEXT NULL,
  field_details JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  usage_count INTEGER DEFAULT 0,
  CONSTRAINT event_custom_fields_pkey PRIMARY KEY (id)
);

-- Add comments to document the table and columns
COMMENT ON TABLE event_custom_fields IS 'Reusable custom field templates for event registration';
COMMENT ON COLUMN event_custom_fields.name IS 'Display name for the custom field template';
COMMENT ON COLUMN event_custom_fields.description IS 'Description of what this field is used for';
COMMENT ON COLUMN event_custom_fields.field_details IS 'JSON configuration for the field (type, validation, options, etc.)';
COMMENT ON COLUMN event_custom_fields.is_active IS 'Whether this field template is available for use';
COMMENT ON COLUMN event_custom_fields.usage_count IS 'Number of times this field has been used in events';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_event_custom_fields_created_by ON event_custom_fields(created_by);
CREATE INDEX IF NOT EXISTS idx_event_custom_fields_is_active ON event_custom_fields(is_active);
CREATE INDEX IF NOT EXISTS idx_event_custom_fields_name ON event_custom_fields(name);
CREATE INDEX IF NOT EXISTS idx_event_custom_fields_field_details ON event_custom_fields USING GIN(field_details);

-- Enable Row Level Security
ALTER TABLE event_custom_fields ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can view all active custom fields
CREATE POLICY "Users can view active custom fields" ON event_custom_fields
  FOR SELECT USING (is_active = true);

-- Users can view their own custom fields (including inactive ones)
CREATE POLICY "Users can view own custom fields" ON event_custom_fields
  FOR SELECT USING (created_by = auth.uid());

-- Users can create custom fields
CREATE POLICY "Users can create custom fields" ON event_custom_fields
  FOR INSERT WITH CHECK (created_by = auth.uid());

-- Users can update their own custom fields
CREATE POLICY "Users can update own custom fields" ON event_custom_fields
  FOR UPDATE USING (created_by = auth.uid());

-- Users can delete their own custom fields
CREATE POLICY "Users can delete own custom fields" ON event_custom_fields
  FOR DELETE USING (created_by = auth.uid());

-- Admins can manage all custom fields
CREATE POLICY "Admins can manage all custom fields" ON event_custom_fields
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users u
      JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.id = auth.uid() AND ur.role_name = 'admin'
    )
  );

-- Insert some default custom field templates
INSERT INTO event_custom_fields (name, description, field_details, created_by) VALUES
  (
    'Emergency Contact',
    'Emergency contact phone number for participants',
    '{
      "type": "phone",
      "label": "Emergency Contact Number",
      "required": true,
      "placeholder": "e.g., +60123456789",
      "validation": {
        "pattern": "^[\\+]?[0-9\\s\\-\\(\\)]{8,}$",
        "minLength": 8
      }
    }'::jsonb,
    NULL
  ),
  (
    'T-Shirt Size',
    'T-shirt size selection for events with merchandise',
    '{
      "type": "select",
      "label": "T-Shirt Size",
      "required": true,
      "placeholder": "Select your t-shirt size",
      "options": ["XS", "S", "M", "L", "XL", "2XL", "3XL", "4XL"]
    }'::jsonb,
    NULL
  ),
  (
    'Dietary Requirements',
    'Special dietary requirements or allergies',
    '{
      "type": "textarea",
      "label": "Dietary Requirements",
      "required": false,
      "placeholder": "Please specify any dietary requirements or allergies",
      "validation": {
        "maxLength": 500
      }
    }'::jsonb,
    NULL
  ),
  (
    'Company/Organization',
    'Company or organization name for corporate events',
    '{
      "type": "text",
      "label": "Company/Organization",
      "required": false,
      "placeholder": "Enter your company or organization name",
      "validation": {
        "maxLength": 100
      }
    }'::jsonb,
    NULL
  ),
  (
    'Age Group',
    'Age group selection for age-specific events',
    '{
      "type": "select",
      "label": "Age Group",
      "required": true,
      "placeholder": "Select your age group",
      "options": ["Under 18", "18-25", "26-35", "36-45", "46-55", "56-65", "Over 65"]
    }'::jsonb,
    NULL
  );

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_event_custom_fields_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_event_custom_fields_updated_at
  BEFORE UPDATE ON event_custom_fields
  FOR EACH ROW
  EXECUTE FUNCTION update_event_custom_fields_updated_at();
