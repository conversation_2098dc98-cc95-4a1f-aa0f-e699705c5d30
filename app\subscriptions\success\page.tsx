"use client"

import { useEffect, useState } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Loader2, AlertCircle } from "lucide-react"
import MainNav from "@/components/main-nav"
import Footer from "@/components/footer"
import { useToast } from "@/hooks/use-toast"

export default function SubscriptionSuccessPage() {
  const [isVerifying, setIsVerifying] = useState(true)
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'failed'>('pending')
  const [subscriptionDetails, setSubscriptionDetails] = useState<any>(null)
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()

  const planId = searchParams.get('plan')
  const transactionId = searchParams.get('transaction_id') || searchParams.get('billplz[id]')

  useEffect(() => {
    if (transactionId) {
      verifyPayment()
    } else {
      // If no transaction ID, assume it's a free plan or direct success
      setIsVerifying(false)
      setVerificationStatus('success')
    }
  }, [transactionId])

  const verifyPayment = async () => {
    try {
      const response = await fetch('/api/subscriptions/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId,
          planId,
        }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setVerificationStatus('success')
        setSubscriptionDetails(data.subscription)
        toast({
          title: "Subscription Activated",
          description: "Your subscription has been successfully activated!",
        })
      } else {
        setVerificationStatus('failed')
        toast({
          title: "Verification Failed",
          description: data.error || "Failed to verify payment",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error verifying payment:', error)
      setVerificationStatus('failed')
      toast({
        title: "Error",
        description: "An error occurred while verifying your payment",
        variant: "destructive",
      })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleRetryVerification = () => {
    setIsVerifying(true)
    setVerificationStatus('pending')
    verifyPayment()
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        {/* Hero Section with Purple Background */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-purple-100 to-indigo-100 dark:from-purple-950 dark:to-indigo-950">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                  Subscription Status
                </h1>
                <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
                  {isVerifying
                    ? "We're processing your subscription..."
                    : verificationStatus === 'success'
                      ? "Welcome to mTicket.my!"
                      : "There was an issue with your subscription"
                  }
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Status Section */}
        <div className="container mx-auto py-12 px-4">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  {isVerifying ? (
                    <Loader2 className="h-16 w-16 animate-spin text-primary" />
                  ) : verificationStatus === 'success' ? (
                    <CheckCircle className="h-16 w-16 text-green-500" />
                  ) : (
                    <AlertCircle className="h-16 w-16 text-destructive" />
                  )}
                </div>
                <CardTitle className="text-2xl">
                  {isVerifying
                    ? "Processing Your Subscription"
                    : verificationStatus === 'success'
                      ? "Subscription Successful!"
                      : "Subscription Failed"
                  }
                </CardTitle>
                <CardDescription>
                  {isVerifying
                    ? "Please wait while we verify your payment and activate your subscription."
                    : verificationStatus === 'success'
                      ? "Your subscription has been successfully activated. You now have access to all premium features."
                      : "We encountered an issue while processing your subscription. Please try again or contact support."
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-6">
                {subscriptionDetails && (
                  <div className="bg-muted p-4 rounded-lg">
                    <h3 className="font-semibold mb-2">Subscription Details</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Plan:</strong> {subscriptionDetails.type}</p>
                      <p><strong>Status:</strong> {subscriptionDetails.isActive ? 'Active' : 'Inactive'}</p>
                      <p><strong>Valid Until:</strong> {new Date(subscriptionDetails.endDate).toLocaleDateString()}</p>
                    </div>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  {verificationStatus === 'success' ? (
                    <>
                      <Button asChild size="lg">
                        <Link href="/dashboard">Go to Dashboard</Link>
                      </Button>
                      <Button asChild variant="outline" size="lg">
                        <Link href="/pricing">View Plans</Link>
                      </Button>
                    </>
                  ) : verificationStatus === 'failed' ? (
                    <>
                      <Button onClick={handleRetryVerification} size="lg">
                        Retry Verification
                      </Button>
                      <Button asChild variant="outline" size="lg">
                        <Link href="/contact">Contact Support</Link>
                      </Button>
                    </>
                  ) : (
                    <div className="text-muted-foreground">
                      This may take a few moments...
                    </div>
                  )}
                </div>

                {transactionId && (
                  <div className="text-sm text-muted-foreground">
                    <p>Transaction ID: {transactionId}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
