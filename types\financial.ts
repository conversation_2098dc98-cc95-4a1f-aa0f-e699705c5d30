/**
 * Financial Transaction Types
 * @deprecated Use Transaction interface from lib/db/supabase-schema.ts instead
 * This interface is kept for backward compatibility only
 */

export interface FinancialTransaction {
  id: string;
  user_id: string;
  event_id?: string | null;
  registration_id?: string | null;
  transaction_type: 'payment' | 'refund' | 'withdrawal' | 'system_fee' | 'payout' | 'registration_payment';
  amount: number;
  fee_amount?: number | null;
  net_amount?: number | null;
  currency?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'paid';
  payment_method?: string | null;
  payment_id?: string | null;
  gateway_transaction_id?: string | null;
  payment_gateway_id?: string | null; // Reference to the payment gateway used
  invoice_number?: string | null;
  receipt_number?: string | null;
  group_transaction_id?: string | null;
  processed_at?: string | null;
  created_at?: string;
  updated_at?: string;
  metadata?: Record<string, any>;
}

export interface PaymentGatewaySetting {
  id: string;
  gateway_name: string;
  is_enabled: boolean;
  is_test_mode: boolean;
  configuration: Record<string, any>;
  test_configuration: Record<string, any>;
  live_configuration: Record<string, any>;
  display_order: number;
  created_at: string;
  updated_at: string;
  created_by?: string | null;
  updated_by?: string | null;
}

export interface PaymentRequest {
  amount: number;
  currency: string;
  description: string;
  customer_email: string;
  customer_name: string;
  reference_id: string;
  success_url: string;
  cancel_url: string;
  payment_gateway_id: string; // Added to link to the payment gateway
}

export interface PaymentResponse {
  success: boolean;
  payment_url?: string;
  transaction_id?: string;
  error?: string;
  payment_gateway_id?: string; // Added to track which gateway processed the payment
}