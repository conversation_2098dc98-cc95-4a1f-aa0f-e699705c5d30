"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  CalendarIcon, Download, Search, User, Calendar, LogIn, Activity,
  Settings, CreditCard, FileText, CheckCircle, AlertCircle,
  Building, Webhook, Check, ChevronsUpDown, X, Award, ExternalLink
} from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { supabase } from "@/lib/supabase"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { useToast } from "@/components/ui/use-toast"
import { ActivityCategory } from "@/utils/activity-logger"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"

// Define the type for activity logs with foreign key relationships
interface ActivityLog {
  id: string
  user_id?: string
  user_name?: string
  action: string
  entity_type: string
  entity_id?: string
  details: Record<string, any>
  category: string
  ip_address?: string
  user_agent?: string
  created_at: string
  // Foreign key relationships
  event_id?: string
  registration_id?: string
  organization_id?: string
  certificate_id?: string
  subscription_id?: string
  target_user_id?: string
  payment_id?: string
  session_id?: string
  // Related entity data
  event?: {
    id: string
    title: string
    slug: string
  }
  registration?: {
    id: string
    attendee_name: string
    attendee_email: string
  }
  organization?: {
    id: string
    name: string
  }
  certificate?: {
    id: string
    participant_name: string
  }
  target_user?: {
    id: string
    full_name: string
    email: string
  }
}

export default function ActivityLogsPage() {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [logType, setLogType] = useState("all")
  const [selectedUser, setSelectedUser] = useState<string | null>(null)
  const [selectedUserDetails, setSelectedUserDetails] = useState<{ id: string, name: string, email: string } | null>(null)
  const [users, setUsers] = useState<{ id: string, name: string, email: string }[]>([])
  const [userSearchQuery, setUserSearchQuery] = useState("")
  const [userSearchOpen, setUserSearchOpen] = useState(false)
  const userSearchTimeout = useRef<NodeJS.Timeout | null>(null)
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [logs, setLogs] = useState<ActivityLog[]>([])
  const [loading, setLoading] = useState(true)
  const [usersLoading, setUsersLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalLogs, setTotalLogs] = useState(0)
  const logsPerPage = 20

  // Check what columns exist in activity_logs table
  const checkTableColumns = async () => {
    try {
      // Try to fetch one row to see what columns are available
      const { data } = await supabase
        .from("activity_logs")
        .select("*")
        .limit(1)

      if (data && data.length > 0) {
        console.log("Available columns in activity_logs:", Object.keys(data[0]))
        return Object.keys(data[0])
      }
      return []
    } catch (error) {
      console.log("Error checking table columns:", error)
      return []
    }
  }

  // Fetch logs from the database
  const fetchLogs = async () => {
    setLoading(true)
    try {
      console.log("Fetching activity logs...")

      // Check available columns first (for debugging)
      await checkTableColumns()
      console.log("Building query with basic columns...")

      // Get current user to check role
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        console.error("Session error:", sessionError)
        throw sessionError
      }

      console.log("Session data:", sessionData)

      // Get user details including role
      let userRole = "unknown"
      if (sessionData?.session?.user?.id) {
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("role")
          .eq("id", sessionData.session.user.id)
          .single()

        if (userError) {
          console.error("Error fetching user role:", userError)
        } else {
          userRole = userData?.role || "unknown"
          console.log("User role:", userRole)
        }
      }

      // Build the query with basic fields first
      let query = supabase
        .from("activity_logs")
        .select(`
          id,
          user_id,
          action,
          entity_type,
          entity_id,
          details,
          category,
          ip_address,
          user_agent,
          created_at
        `, { count: 'exact' })
        .order('created_at', { ascending: false })

      // Apply filters
      if (logType !== "all") {
        query = query.eq('category', logType)
      }

      // Filter by user if a specific user is selected
      if (selectedUser) {
        query = query.eq('user_id', selectedUser)
      }

      if (searchQuery) {
        query = query.or(`action.ilike.%${searchQuery}%,details.ilike.%${searchQuery}%`)
      }

      if (dateRange.from) {
        const fromDate = new Date(dateRange.from)
        fromDate.setHours(0, 0, 0, 0)
        query = query.gte('created_at', fromDate.toISOString())

        if (dateRange.to) {
          const toDate = new Date(dateRange.to)
          toDate.setHours(23, 59, 59, 999)
          query = query.lte('created_at', toDate.toISOString())
        }
      }

      // Apply pagination
      const from = (page - 1) * logsPerPage
      const to = from + logsPerPage - 1
      query = query.range(from, to)

      console.log("Executing query...")

      // Execute the query
      const { data, error, count } = await query

      console.log("Query result:", { data, error, count })

      if (error) {
        throw error
      }

      // Process the data to include user names and foreign key relationships
      const processedLogs = await Promise.all(data.map(async (log) => {
        let userName = 'Unknown User';

        // If we have a user_id, fetch the user details
        if (log.user_id) {
          try {
            const { data: userData, error: userError } = await supabase
              .from("users")
              .select("full_name, email")
              .eq("id", log.user_id)
              .single();

            if (!userError && userData) {
              userName = userData.full_name || userData.email;
            }
          } catch (err) {
            console.error("Error fetching user data for log:", err);
          }
        }

        return {
          ...log,
          user_name: userName,
          // Foreign key relationships - will be undefined for now until columns exist
          event: undefined,
          registration: undefined,
          organization: undefined,
          certificate: undefined,
          target_user: undefined,
        };
      })) as ActivityLog[];

      console.log("Processed logs:", processedLogs);

      setLogs(processedLogs);

      // Set pagination info
      if (count !== null) {
        setTotalLogs(count);
        setTotalPages(Math.ceil(count / logsPerPage));
      }
    } catch (error) {
      console.error("Error fetching activity logs:", error)

      // More specific error message
      let errorMessage = "Failed to fetch activity logs."
      if (error instanceof Error) {
        errorMessage += ` Error: ${error.message}`
      } else if (typeof error === 'object' && error !== null) {
        errorMessage += ` Details: ${JSON.stringify(error)}`
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Fetch users for the filter dropdown with search support
  const fetchUsers = async (searchTerm: string = "") => {
    setUsersLoading(true)
    try {
      let query = supabase
        .from("users")
        .select("id, full_name, email")

      // Apply search filter if provided
      if (searchTerm) {
        query = query.or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
      }

      // Limit results and order by name
      const { data, error } = await query
        .order("full_name", { ascending: true })
        .limit(10) // Limit to 10 results for better performance

      if (error) {
        throw error
      }

      // Format users for the dropdown
      const formattedUsers = data.map(user => ({
        id: user.id,
        name: user.full_name || "Unknown",
        email: user.email
      }))

      setUsers(formattedUsers)
    } catch (error) {
      console.error("Error fetching users:", error)
      toast({
        title: "Error",
        description: "Failed to fetch users for filtering",
        variant: "destructive",
      })
    } finally {
      setUsersLoading(false)
    }
  }

  // Handle user search with debounce
  const handleUserSearch = (value: string) => {
    setUserSearchQuery(value)

    // Clear any existing timeout
    if (userSearchTimeout.current) {
      clearTimeout(userSearchTimeout.current)
    }

    // Set a new timeout to debounce the search
    userSearchTimeout.current = setTimeout(() => {
      fetchUsers(value)
    }, 300) // 300ms debounce
  }

  // Fetch logs when filters or pagination changes
  useEffect(() => {
    fetchLogs()
  }, [page, logType, selectedUser, dateRange, searchQuery])

  // Fetch initial users on component mount
  useEffect(() => {
    fetchUsers()
  }, [])

  // Update selectedUserDetails when selectedUser changes
  useEffect(() => {
    if (selectedUser) {
      const userDetails = users.find(user => user.id === selectedUser)
      if (userDetails) {
        setSelectedUserDetails(userDetails)
      } else {
        // If user details not found in current list, fetch them
        const fetchUserDetails = async () => {
          try {
            const { data, error } = await supabase
              .from("users")
              .select("id, full_name, email")
              .eq("id", selectedUser)
              .single()

            if (error) throw error

            if (data) {
              const userDetails = {
                id: data.id,
                name: data.full_name || "Unknown",
                email: data.email
              }
              setSelectedUserDetails(userDetails)
            }
          } catch (error) {
            console.error("Error fetching user details:", error)
          }
        }

        fetchUserDetails()
      }
    } else {
      setSelectedUserDetails(null)
    }
  }, [selectedUser, users])

  // Handle search with debounce
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchQuery(value)
  }

  // Handle export logs
  const handleExportLogs = async () => {
    try {
      // Build the query with current filters but no pagination - basic fields for now
      let query = supabase
        .from("activity_logs")
        .select(`
          id,
          user_id,
          action,
          entity_type,
          entity_id,
          details,
          category,
          ip_address,
          user_agent,
          created_at
        `)
        .order('created_at', { ascending: false })

      // Apply filters
      if (logType !== "all") {
        query = query.eq('category', logType)
      }

      // Filter by user if a specific user is selected
      if (selectedUser) {
        query = query.eq('user_id', selectedUser)
      }

      if (searchQuery) {
        query = query.or(`action.ilike.%${searchQuery}%,details.ilike.%${searchQuery}%`)
      }

      if (dateRange.from) {
        const fromDate = new Date(dateRange.from)
        fromDate.setHours(0, 0, 0, 0)
        query = query.gte('created_at', fromDate.toISOString())

        if (dateRange.to) {
          const toDate = new Date(dateRange.to)
          toDate.setHours(23, 59, 59, 999)
          query = query.lte('created_at', toDate.toISOString())
        }
      }

      // Execute the query
      const { data, error } = await query

      if (error) {
        throw error
      }

      // Process the data for CSV export - fetch user info separately
      const processedLogs = await Promise.all(data.map(async (log) => {
        let userName = 'Unknown User';

        // If we have a user_id, fetch the user details
        if (log.user_id) {
          try {
            const { data: userData, error: userError } = await supabase
              .from("users")
              .select("full_name, email")
              .eq("id", log.user_id)
              .single();

            if (!userError && userData) {
              userName = userData.full_name || userData.email;
            }
          } catch (err) {
            console.error("Error fetching user data for log:", err);
          }
        }

        return {
          id: log.id,
          timestamp: new Date(log.created_at).toLocaleString(),
          user: userName,
          action: formatAction(log.action),
          category: log.category,
          entity_type: log.entity_type || 'N/A',
          entity_id: log.entity_id || 'N/A',
          details: JSON.stringify(log.details),
          ip_address: log.ip_address || 'N/A',
        };
      }))

      // Convert to CSV with basic data
      const headers = [
        'ID', 'Timestamp', 'User', 'Action', 'Category',
        'Entity Type', 'Entity ID', 'Details', 'IP Address'
      ]
      const csvContent = [
        headers.join(','),
        ...processedLogs.map(log => [
          log.id,
          `"${log.timestamp}"`,
          `"${log.user}"`,
          `"${log.action}"`,
          `"${log.category}"`,
          `"${log.entity_type}"`,
          `"${log.entity_id}"`,
          `"${log.details.replace(/"/g, '""')}"`,
          `"${log.ip_address}"`,
        ].join(','))
      ].join('\n')

      // Create a download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.setAttribute('href', url)
      link.setAttribute('download', `activity-logs-${new Date().toISOString().split('T')[0]}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Success",
        description: "Activity logs exported successfully",
      })
    } catch (error) {
      console.error("Error exporting logs:", error)
      toast({
        title: "Error",
        description: "Failed to export activity logs",
        variant: "destructive",
      })
    }
  }

  // Handle page change
  // Reset all filters
  const resetFilters = () => {
    setLogType("all")
    setSelectedUser(null)
    setSelectedUserDetails(null)
    setUserSearchQuery("")
    setDateRange({ from: undefined, to: undefined })
    setSearchQuery("")
    setPage(1)
  }

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages) {
      setPage(newPage)
    }
  }

  // Handle viewing complete audit trail for a specific user
  const handleViewUserAuditTrail = (userId: string, userName: string) => {
    // Set the user filter to show only this user's activities
    setSelectedUser(userId)
    setSelectedUserDetails({ id: userId, name: userName, email: '' })

    // Reset other filters to show all activities for this user
    setLogType("all")
    setSearchQuery("")
    setDateRange({ from: undefined, to: undefined })
    setPage(1)

    // Show toast notification
    toast({
      title: "User Audit Trail",
      description: `Showing complete audit trail for ${userName}`,
    })
  }

  // Format action string for display
  const formatAction = (action: string) => {
    return action
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  // Get icon for log action
  const getActionIcon = (category: string) => {
    switch (category) {
      case ActivityCategory.AUTH:
        return <LogIn className="h-4 w-4" />
      case ActivityCategory.EVENT:
        return <Calendar className="h-4 w-4" />
      case ActivityCategory.USER:
        return <User className="h-4 w-4" />
      case ActivityCategory.REGISTRATION:
        return <Calendar className="h-4 w-4" />
      case ActivityCategory.EXPORT:
        return <Download className="h-4 w-4" />
      case ActivityCategory.PAYMENT:
        return <CreditCard className="h-4 w-4" />
      case ActivityCategory.CERTIFICATE:
        return <FileText className="h-4 w-4" />
      case ActivityCategory.SETTINGS:
        return <Settings className="h-4 w-4" />
      case ActivityCategory.ATTENDANCE:
        return <CheckCircle className="h-4 w-4" />
      case ActivityCategory.SYSTEM:
        return <AlertCircle className="h-4 w-4" />
      case ActivityCategory.ORGANIZATION:
        return <Building className="h-4 w-4" />
      case ActivityCategory.WEBHOOK:
        return <Webhook className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }



  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Enhanced Activity Logs</h2>
          <p className="text-muted-foreground">Comprehensive audit trail with foreign key relationships - track complete user journeys from registration to certificate generation</p>

          {/* Show active filters */}
          {selectedUserDetails && (
            <div className="mt-2 flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1 bg-blue-50 border-blue-200">
                <User className="h-3 w-3 text-blue-600" />
                <span className="text-blue-800">
                  Showing audit trail for: {selectedUserDetails.name}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-1 hover:bg-blue-100"
                  onClick={() => {
                    setSelectedUser(null)
                    setSelectedUserDetails(null)
                  }}
                >
                  <span className="sr-only">Clear user filter</span>
                  <X className="h-3 w-3 text-blue-600" />
                </Button>
              </Badge>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="gap-2"
            onClick={resetFilters}
            disabled={logType === "all" && !selectedUser && !dateRange.from && !searchQuery}
          >
            <span className="flex items-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                <path d="M3 3v5h5"></path>
              </svg>
              Reset Filters
            </span>
          </Button>
          <Button variant="outline" size="sm" className="gap-2" onClick={handleExportLogs}>
            <Download className="h-4 w-4" />
            Export Logs
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>System Activity</CardTitle>
          <CardDescription>View comprehensive activity logs with foreign key relationships, complete user journeys, and detailed audit trails across the platform</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="grid w-full items-center gap-1.5">
              <Select value={logType} onValueChange={setLogType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select log type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Activities</SelectItem>
                  <SelectItem value={ActivityCategory.AUTH}>Authentication</SelectItem>
                  <SelectItem value={ActivityCategory.EVENT}>Event Activities</SelectItem>
                  <SelectItem value={ActivityCategory.USER}>User Activities</SelectItem>
                  <SelectItem value={ActivityCategory.REGISTRATION}>Registration Activities</SelectItem>
                  <SelectItem value={ActivityCategory.PAYMENT}>Payment Activities</SelectItem>
                  <SelectItem value={ActivityCategory.CERTIFICATE}>Certificate Activities</SelectItem>
                  <SelectItem value={ActivityCategory.EXPORT}>Export Activities</SelectItem>
                  <SelectItem value={ActivityCategory.SETTINGS}>Settings Changes</SelectItem>
                  <SelectItem value={ActivityCategory.ATTENDANCE}>Attendance Activities</SelectItem>
                  <SelectItem value={ActivityCategory.ORGANIZATION}>Organization Activities</SelectItem>
                  <SelectItem value={ActivityCategory.WEBHOOK}>Webhook Activities</SelectItem>
                  <SelectItem value={ActivityCategory.SYSTEM}>System Activities</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* User filter searchable combobox */}
            <div className="grid w-full items-center gap-1.5">
              <Popover open={userSearchOpen} onOpenChange={setUserSearchOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={userSearchOpen}
                    className="w-full justify-between"
                    disabled={usersLoading}
                  >
                    {selectedUserDetails ? (
                      <div className="flex items-center gap-2 truncate">
                        <User className="h-4 w-4 shrink-0" />
                        <span className="truncate">{selectedUserDetails.name}</span>
                        <span className="text-xs text-muted-foreground truncate">({selectedUserDetails.email})</span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">Search for a user...</span>
                    )}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <Command>
                    <CommandInput
                      placeholder="Search users..."
                      value={userSearchQuery}
                      onValueChange={handleUserSearch}
                    />
                    {selectedUser && (
                      <div className="px-2 pt-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 text-xs w-full justify-start text-muted-foreground"
                          onClick={() => {
                            setSelectedUser(null)
                            setSelectedUserDetails(null)
                            setUserSearchOpen(false)
                          }}
                        >
                          <X className="mr-2 h-3 w-3" />
                          Clear selection
                        </Button>
                      </div>
                    )}
                    <CommandList>
                      {usersLoading ? (
                        <div className="flex items-center justify-center py-6">
                          <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>Loading users...</span>
                        </div>
                      ) : (
                        <CommandGroup>
                          {users.length === 0 ? (
                            <CommandEmpty>No users found. Try a different search.</CommandEmpty>
                          ) : (
                            users.map(user => (
                              <CommandItem
                                key={user.id}
                                value={user.id}
                                onSelect={() => {
                                  setSelectedUser(user.id)
                                  setSelectedUserDetails(user)
                                  setUserSearchOpen(false)
                                }}
                              >
                                <div className="flex items-center">
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedUser === user.id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  <div className="flex flex-col">
                                    <span>{user.name}</span>
                                    <span className="text-xs text-muted-foreground">{user.email}</span>
                                  </div>
                                </div>
                              </CommandItem>
                            ))
                          )}
                        </CommandGroup>
                      )}
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid w-full items-center gap-1.5">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dateRange.from && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Select date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={(range) => setDateRange({ from: range?.from, to: range?.to })}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid w-full items-center gap-1.5">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search logs..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Enhanced Details & Relationships</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      Loading activity logs...
                    </TableCell>
                  </TableRow>
                ) : logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      No activity logs found
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="whitespace-nowrap">{new Date(log.created_at).toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span>{log.user_name}</span>
                          {log.user_id && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 hover:bg-blue-50"
                                    onClick={() => handleViewUserAuditTrail(log.user_id!, log.user_name || 'Unknown User')}
                                  >
                                    <ExternalLink className="h-3 w-3 text-blue-600" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>View complete audit trail for {log.user_name}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="flex items-center gap-1">
                            {getActionIcon(log.category)}
                            <span>{formatAction(log.action)}</span>
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs space-y-1">
                          {/* Foreign key relationships - prioritized display */}
                          {log.event && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3 text-blue-500" />
                              <span className="text-sm font-medium text-blue-700">{log.event.title}</span>
                            </div>
                          )}

                          {log.registration && (
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3 text-green-500" />
                              <span className="text-sm font-medium text-green-700">{log.registration.attendee_name}</span>
                              <span className="text-xs text-muted-foreground">({log.registration.attendee_email})</span>
                            </div>
                          )}

                          {log.certificate && (
                            <div className="flex items-center gap-1">
                              <Award className="h-3 w-3 text-purple-500" />
                              <span className="text-sm font-medium text-purple-700">{log.certificate.participant_name}</span>
                            </div>
                          )}

                          {log.organization && (
                            <div className="flex items-center gap-1">
                              <Building className="h-3 w-3 text-orange-500" />
                              <span className="text-sm font-medium text-orange-700">{log.organization.name}</span>
                            </div>
                          )}

                          {log.details?.transaction_id && (
                            <div className="flex items-center gap-1">
                              <CreditCard className="h-3 w-3 text-emerald-500" />
                              <span className="text-sm font-medium text-emerald-700">Transaction: {log.details.transaction_id}</span>
                            </div>
                          )}

                          {log.target_user && (
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3 text-red-500" />
                              <span className="text-sm font-medium text-red-700">Target: {log.target_user.full_name}</span>
                            </div>
                          )}

                          {/* Legacy details display */}
                          {log.details.event_name && !log.event && <span className="text-sm font-medium">{log.details.event_name}</span>}

                          {/* Login/Logout details */}
                          {log.action === 'login' && (
                            <div className="text-sm text-muted-foreground">
                              Method: {log.details.login_method || 'credentials'}
                              {log.details.ip && <span> from {log.details.ip}</span>}
                              {log.details.device && <span> using {log.details.device}</span>}
                            </div>
                          )}

                          {log.action === 'logout' && (
                            <div className="text-sm text-muted-foreground">
                              User role: {log.details.role || 'user'}
                            </div>
                          )}

                          {log.action === 'register' && (
                            <div className="text-sm text-muted-foreground">
                              Method: {log.details.registration_method || 'email'}
                              {log.details.role && <span>, Role: {log.details.role}</span>}
                            </div>
                          )}

                          {/* Status information */}
                          {log.details.status && (
                            <div className="text-sm text-muted-foreground">
                              Status: {log.details.status}
                            </div>
                          )}

                          {log.details.status_change && (
                            <div className="text-sm text-muted-foreground">
                              Status change: {log.details.status_change}
                            </div>
                          )}

                          {/* Updated fields */}
                          {log.details.fields_updated && (
                            <div className="text-sm text-muted-foreground">
                              Updated: {Array.isArray(log.details.fields_updated)
                                ? log.details.fields_updated.join(", ")
                                : log.details.fields_updated}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(page - 1)}
                      className={page === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    // Show pages around the current page
                    let pageNum = page;
                    if (page <= 3) {
                      // At the beginning, show first 5 pages
                      pageNum = i + 1;
                    } else if (page >= totalPages - 2) {
                      // At the end, show last 5 pages
                      pageNum = totalPages - 4 + i;
                    } else {
                      // In the middle, show 2 pages before and 2 pages after
                      pageNum = page - 2 + i;
                    }

                    // Ensure page number is within valid range
                    if (pageNum > 0 && pageNum <= totalPages) {
                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            onClick={() => handlePageChange(pageNum)}
                            isActive={pageNum === page}
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    }
                    return null;
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(page + 1)}
                      className={page === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
              <div className="text-center text-sm text-muted-foreground mt-2">
                Showing {Math.min(totalLogs, (page - 1) * logsPerPage + 1)} to {Math.min(totalLogs, page * logsPerPage)} of {totalLogs} logs
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
