"use client"

import { useState, useEffect } from "react"
import { useFieldArray, useFormContext } from "react-hook-form"
import { Plus, Trash2, GripVertical, Settings, Library, PlusCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { supabase } from "@/lib/supabase"
import type { EventCustomField, CustomField } from "@/lib/db/supabase-schema"

interface CustomFieldsConfigProps {
  name: string // Form field name for the custom fields array
}

const fieldTypes = [
  { value: 'text', label: 'Text Input' },
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone Number' },
  { value: 'number', label: 'Number' },
  { value: 'textarea', label: 'Text Area' },
  { value: 'select', label: 'Dropdown' },
  { value: 'checkbox', label: 'Checkbox' },
]

export function CustomFieldsConfig({ name }: CustomFieldsConfigProps) {
  const { control, watch } = useFormContext()
  const { fields, append, remove, move } = useFieldArray({
    control,
    name,
  })

  const [expandedFields, setExpandedFields] = useState<Set<string>>(new Set())
  const [availableTemplates, setAvailableTemplates] = useState<EventCustomField[]>([])
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Fetch available custom field templates
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const { data, error } = await supabase
          .from("event_custom_fields")
          .select("*")
          .eq("is_active", true)
          .order("usage_count", { ascending: false })

        if (error) {
          console.error("Error fetching custom field templates:", error)
        } else {
          setAvailableTemplates(data || [])
        }
      } catch (error) {
        console.error("Error fetching templates:", error)
      }
    }

    fetchTemplates()
  }, [])

  const addField = () => {
    const newField: CustomField = {
      id: `field_${Date.now()}`,
      label: '',
      type: 'text',
      required: false,
      placeholder: '',
      order: fields.length,
    }
    append(newField)
    setExpandedFields(prev => new Set([...prev, newField.id]))
  }

  const addFieldFromTemplate = (template: EventCustomField) => {
    const newField: CustomField = {
      id: `field_${Date.now()}`,
      label: template.field_details.label,
      type: template.field_details.type,
      required: template.field_details.required,
      placeholder: template.field_details.placeholder,
      options: template.field_details.options,
      validation: template.field_details.validation,
      order: fields.length,
    }
    append(newField)
    setExpandedFields(prev => new Set([...prev, newField.id]))
    setIsTemplateDialogOpen(false)

    // Update usage count
    updateTemplateUsage(template.id)
  }

  const updateTemplateUsage = async (templateId: number) => {
    try {
      await supabase
        .from("event_custom_fields")
        .update({ usage_count: supabase.sql`usage_count + 1` })
        .eq("id", templateId)
    } catch (error) {
      console.error("Error updating template usage:", error)
    }
  }

  const toggleExpanded = (fieldId: string) => {
    setExpandedFields(prev => {
      const newSet = new Set(prev)
      if (newSet.has(fieldId)) {
        newSet.delete(fieldId)
      } else {
        newSet.add(fieldId)
      }
      return newSet
    })
  }

  const watchedFields = watch(name) as CustomField[]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Custom Registration Fields</h3>
          <p className="text-sm text-muted-foreground">
            Add custom fields to collect additional information from participants
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
            <DialogTrigger asChild>
              <Button type="button" variant="outline" size="sm">
                <Library className="h-4 w-4 mr-2" />
                From Template
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add Field from Template</DialogTitle>
              </DialogHeader>
              <Tabs defaultValue="templates" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="templates">Available Templates</TabsTrigger>
                  <TabsTrigger value="create">Create New Template</TabsTrigger>
                </TabsList>

                <TabsContent value="templates" className="space-y-4">
                  {availableTemplates.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Library className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No templates available</p>
                      <p className="text-sm">Create your first template to reuse across events</p>
                    </div>
                  ) : (
                    <div className="grid gap-3">
                      {availableTemplates.map((template) => (
                        <Card key={template.id} className="cursor-pointer hover:bg-muted/50 transition-colors">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium">{template.name}</h4>
                                  <Badge variant="secondary" className="text-xs">
                                    {fieldTypes.find(t => t.value === template.field_details.type)?.label}
                                  </Badge>
                                  {template.field_details.required && (
                                    <Badge variant="destructive" className="text-xs">Required</Badge>
                                  )}
                                  <Badge variant="outline" className="text-xs">
                                    Used {template.usage_count} times
                                  </Badge>
                                </div>
                                {template.description && (
                                  <p className="text-sm text-muted-foreground mb-2">{template.description}</p>
                                )}
                                <div className="text-xs text-muted-foreground">
                                  <strong>Label:</strong> {template.field_details.label}
                                  {template.field_details.placeholder && (
                                    <span className="ml-2"><strong>Placeholder:</strong> {template.field_details.placeholder}</span>
                                  )}
                                </div>
                              </div>
                              <Button
                                type="button"
                                size="sm"
                                onClick={() => addFieldFromTemplate(template)}
                              >
                                <Plus className="h-4 w-4 mr-1" />
                                Add
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="create" className="space-y-4">
                  <div className="text-center py-8 text-muted-foreground">
                    <PlusCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>Template creation feature</p>
                    <p className="text-sm">Coming soon - ability to save custom fields as reusable templates</p>
                  </div>
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>

          <Button type="button" onClick={addField} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Custom
          </Button>
        </div>
      </div>

      {fields.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No custom fields configured</p>
              <p className="text-sm">Click "Add Field" to create custom registration fields</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {fields.map((field, index) => {
            const fieldData = watchedFields?.[index]
            const isExpanded = expandedFields.has(field.id)
            
            return (
              <Card key={field.id}>
                <Collapsible open={isExpanded} onOpenChange={() => toggleExpanded(field.id)}>
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <GripVertical className="h-4 w-4 text-muted-foreground" />
                          <div className="flex items-center gap-2">
                            <CardTitle className="text-sm">
                              {fieldData?.label || `Field ${index + 1}`}
                            </CardTitle>
                            <Badge variant="secondary" className="text-xs">
                              {fieldTypes.find(t => t.value === fieldData?.type)?.label || 'Text'}
                            </Badge>
                            {fieldData?.required && (
                              <Badge variant="destructive" className="text-xs">Required</Badge>
                            )}
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            remove(index)
                            setExpandedFields(prev => {
                              const newSet = new Set(prev)
                              newSet.delete(field.id)
                              return newSet
                            })
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent>
                    <CardContent className="pt-0 space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <FormField
                          control={control}
                          name={`${name}.${index}.label`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Field Label</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g., Emergency Contact" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={control}
                          name={`${name}.${index}.type`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Field Type</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select field type" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {fieldTypes.map((type) => (
                                    <SelectItem key={type.value} value={type.value}>
                                      {type.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={control}
                        name={`${name}.${index}.placeholder`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Placeholder Text (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g., Enter your emergency contact number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {fieldData?.type === 'select' && (
                        <FormField
                          control={control}
                          name={`${name}.${index}.options`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Options (one per line)</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Option 1&#10;Option 2&#10;Option 3"
                                  value={field.value?.join('\n') || ''}
                                  onChange={(e) => {
                                    const options = e.target.value.split('\n').filter(opt => opt.trim())
                                    field.onChange(options)
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      <div className="flex items-center space-x-2">
                        <FormField
                          control={control}
                          name={`${name}.${index}.required`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Required Field</FormLabel>
                              </div>
                            </FormItem>
                          )}
                        />
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}
